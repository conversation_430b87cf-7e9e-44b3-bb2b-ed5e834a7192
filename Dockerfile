# ---- Builder Stage ----
# Use Alpine for builder to match final stage OS for Prisma binaries
FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files and the entire prisma directory first
COPY package.json package-lock.json ./
COPY prisma ./prisma/

# Install build dependencies for Alpine (if any are different from slim, e.g. python, make, g++)
# For Prisma with native extensions, common build tools might be needed.
# openssl-dev is needed for engines typically.
RUN apk add --no-cache openssl-dev python3 make g++

# Install ALL dependencies (including devDependencies needed for build)
RUN npm install

# Generate Prisma Client - uses /app/prisma/schema.prisma
RUN npx prisma generate

# Copy the rest of the application source code
# This will overwrite files if they exist, but package.json, package-lock.json, and prisma dir are already correctly in place.
COPY . .

# No longer passing these specific env vars as build-time args for SvelteKit static envs
# They will be runtime environment variables.

# Ensure SvelteKit's types and tsconfig are generated before building
RUN npx svelte-kit sync

# Ensure the fix script has Unix line endings and is executable
RUN sed -i 's/\r$//' /app/fix_dirname_error.sh && chmod +x /app/fix_dirname_error.sh

# Diagnostic step: List /app, cat package.json, and check fix_dirname_error.sh
RUN echo "--- Listing /app contents ---" && ls -la /app && \
    echo "--- Contents of /app/package.json ---" && cat /app/package.json && \
    echo "--- Details of /app/fix_dirname_error.sh ---" && ls -la /app/fix_dirname_error.sh && \
    echo "--- End of diagnostic step ---"

# Build the SvelteKit application
RUN npm run build


# ---- Final Stage ----
# Use a lightweight Alpine image for the final runtime (LTS version - Node 20)
FROM node:20-alpine

WORKDIR /app

# Set timezone - ARG can be overridden at build time
ARG TZ=Asia/Rangoon
ENV NODE_ENV=production
# Increase SvelteKit's body size limit for file uploads (e.g., 10MB)
ENV BODY_SIZE_LIMIT=10485760

# Install dependencies for supercronic, timezone data, and other utilities
# Added curl for supercronic download, coreutils for sha1sum (usually included but good to be explicit if needed)
RUN apk add --no-cache wget curl ca-certificates tzdata coreutils \
    && cp /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Install supercronic using official instructions
ENV SUPERCRONIC_URL=https://github.com/aptible/supercronic/releases/download/v0.2.33/supercronic-linux-amd64 \
    SUPERCRONIC_SHA1SUM=71b0d58cc53f6bd72cf2f293e09e294b79c666d8 \
    SUPERCRONIC=supercronic-linux-amd64

RUN curl -fsSLO "$SUPERCRONIC_URL" \
 && echo "${SUPERCRONIC_SHA1SUM}  ${SUPERCRONIC}" | sha1sum -c - \
 && chmod +x "$SUPERCRONIC" \
 && mv "$SUPERCRONIC" "/usr/local/bin/${SUPERCRONIC}" \
 && ln -s "/usr/local/bin/${SUPERCRONIC}" /usr/local/bin/supercronic \
 && echo "Supercronic installation successfully completed using official method."

# Create a non-root user and group, and its home directory
# Using -S for system user/group, -D to not assign a password
# -h /home/<USER>
RUN addgroup -S appgroup && \
    adduser -S -D -h /home/<USER>

# Copy the crontab file
COPY cleanup.crontab /app/cleanup.crontab

# Copy necessary artifacts from the builder stage
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/package-lock.json ./package-lock.json
RUN npm i --only=production

# Copy the Prisma schema and the generated client (including engines) from the builder stage
COPY --from=builder /app/prisma/schema.prisma ./prisma/schema.prisma
COPY --from=builder /app/prisma/migrations ./prisma/migrations
COPY --from=builder /app/node_modules/.prisma/client ./node_modules/.prisma/client/
# COPY --from=builder /app/node_modules/@prisma/client/runtime ./node_modules/@prisma/client/runtime/ # Might also be needed

# No need to run `npx prisma generate` again in final stage if engines are copied correctly
# and binaryTarget matches. If schema changes, it's done in builder.

COPY --from=builder /app/build ./build
COPY --from=builder /app/scripts ./scripts

# Create .npm directory within the user's home and set ownership.
# This ensures npm/npx can write cache/config if needed.
RUN mkdir -p /home/<USER>/.npm && \
    chown -R appuser:appgroup /home/<USER>/.npm

# Create upload directories, data directory, and make setup script executable
RUN mkdir -p /app/static/uploads/payment-proofs && \
    mkdir -p /app/data && \
    chmod +x /app/scripts/setup-production-directories.sh && \
    chmod 755 /app/static/uploads && \
    chmod 755 /app/static/uploads/payment-proofs && \
    chmod 755 /app/data

# Change ownership of the app directory to the new user
# This should be done after all files are copied to /app
RUN chown -R appuser:appgroup /app

# Switch to the non-root user
# HOME environment variable will now be /home/<USER>
USER appuser

# Expose the port the app runs on
EXPOSE 3000

# Define the command to run setup, supercronic (in background), and then the application
# The built output is in build/index.js
# `npx prisma migrate deploy` is removed from startup to prevent wiping the persistent DB.
# Migrations should be handled as a separate, one-time operation when a schema change is needed.
CMD ["sh", "-c", "/app/scripts/setup-production-directories.sh && /usr/local/bin/supercronic /app/cleanup.crontab & node build/index.js"]
