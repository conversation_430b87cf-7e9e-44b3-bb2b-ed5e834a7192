#!/bin/sh

# This script modifies build/handler.js generated by SvelteKit adapter-node.
# It assumes that SvelteKit's build process ALREADY INCLUDES the necessary:
#   import { fileURLToPath } from "url";
#   import path from "path";
# at the top of handler.js, making them available in the module's scope.

# We are only adding the globalThis definitions if they are not already set.
# These globals might be needed by other modules imported by handler.js.

TEMP_FILE=$(mktemp)

# Insert the globalThis definitions before the line containing 'await server.init('
# This makes __filename and __dirname available globally if not already defined.
# It relies on 'fileURLToPath' and 'path' being in scope from SvelteKit's own imports.
awk '
  /await server\.init\(/ {
    print "try {";
    print "  if (typeof globalThis.__filename === \"undefined\") {";
    print "    if (typeof fileURLToPath !== \"undefined\" && typeof path !== \"undefined\" && typeof import.meta.url !== \"undefined\") {";
    print "      globalThis.__filename = fileURLToPath(import.meta.url);";
    print "      globalThis.__dirname = path.dirname(globalThis.__filename);";
    print "    } else {";
    print "      console.warn(\"[fix_dirname_error.sh] Could not set __filename/__dirname: fileURLToPath, path, or import.meta.url is not defined in handler.js scope.\");";
    print "    }";
    print "  }";
    print "} catch (e) { console.error(\"[fix_dirname_error.sh] Error setting __filename/__dirname globals in handler.js:\", e); }"
  }
  { print }
' build/handler.js > "$TEMP_FILE" && mv "$TEMP_FILE" build/handler.js
