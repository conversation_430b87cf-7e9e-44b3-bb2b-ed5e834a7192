declare module '@woocommerce/woocommerce-rest-api' {
  export interface WooCommerceRestApiOptions {
    url: string;
    consumerKey: string;
    consumerSecret: string;
    version?: string;
    wpAPIPrefix?: string;
    queryStringAuth?: boolean;
    encoding?: string;
    axiosConfig?: any;
  }

  export default class WooCommerceRestApi {
    constructor(options: WooCommerceRestApiOptions);
    get(endpoint: string, params?: Record<string, any>): Promise<any>;
    post(endpoint: string, data: Record<string, any>, params?: Record<string, any>): Promise<any>;
    put(endpoint: string, data: Record<string, any>, params?: Record<string, any>): Promise<any>;
    delete(endpoint: string, params?: Record<string, any>): Promise<any>;
    options(endpoint: string, params?: Record<string, any>): Promise<any>;
  }
}
