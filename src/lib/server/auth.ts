import crypto from 'crypto';
import { env } from '$env/dynamic/private';

// Store active sessions in memory (in production, use Redis or database)
const activeSessions = new Map<string, { expires: number; created: number }>();

// Clean up expired sessions every hour
setInterval(() => {
  const now = Date.now();
  for (const [token, session] of activeSessions.entries()) {
    if (now > session.expires) {
      activeSessions.delete(token);
    }
  }
}, 60 * 60 * 1000);

// Hash function for password comparison
export function hashPassword(password: string): string {
  return crypto.createHash('sha256').update(password + 'ipman_salt_2024').digest('hex');
}

// Generate secure session token
export function generateSessionToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

// Get admin password from environment or throw error
export function getAdminPasswordHash(): string {
  const envPasswordHash = env.ADMIN_PASSWORD_HASH;
  
  if (envPasswordHash) {
    console.log('Using admin password hash from environment variable');
    return envPasswordHash;
  }
  
  // No default - force proper configuration
  console.error('ADMIN_PASSWORD_HASH not set in environment variables!');
  throw new Error('Admin password not configured. Please set ADMIN_PASSWORD_HASH in .env file.');
}

// Create a new session
export function createSession(token: string): void {
  const expires = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
  activeSessions.set(token, {
    expires,
    created: Date.now()
  });
}

// Verify session token
export function verifyAdminSession(token: string): boolean {
  if (!token) return false;
  
  const session = activeSessions.get(token);
  if (!session) return false;
  
  if (Date.now() > session.expires) {
    activeSessions.delete(token);
    return false;
  }
  
  return true;
}

// Delete a session (logout)
export function deleteSession(token: string): boolean {
  return activeSessions.delete(token);
}

// Get session count (for debugging)
export function getActiveSessionCount(): number {
  return activeSessions.size;
}
