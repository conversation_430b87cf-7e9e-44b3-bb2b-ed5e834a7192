import { env } from '$env/dynamic/private';
// Import necessary types (can also be imported from the original types file if preferred)
import type { OrderBilling, OrderLineItem, OrderMetaData, Order, ProductCategory, ProductVariation, Product } from '$lib/services/woocommerce';

// Custom WooCommerce API client (Server-Side Only)
class WooCommerceClient {
  private baseUrl: string;
  private consumerKey: string;
  private consumerSecret: string;
  private version: string;

  constructor() {
    this.baseUrl = env.WOOCOMMERCE_URL || '';
    this.consumerKey = env.WOOCOMMERCE_CONSUMER_KEY || '';
    this.consumerSecret = env.WOOCOMMERCE_CONSUMER_SECRET || '';
    this.version = 'wc/v3';

    if (!this.baseUrl || !this.consumerKey || !this.consumerSecret) {
      console.error('WooCommerce API credentials are not fully configured in .env');
      // Optionally throw an error or handle appropriately
    }
  }

  private getAuthQueryParams(): string {
    return `consumer_key=${this.consumerKey}&consumer_secret=${this.consumerSecret}`;
  }

  private buildUrl(endpoint: string, params: Record<string, any> = {}): string {
    // Build the base URL
    let url = `${this.baseUrl}/wp-json/${this.version}/${endpoint}?${this.getAuthQueryParams()}`;

    // Add additional query parameters
    Object.keys(params).forEach(key => {
      url += `&${key}=${encodeURIComponent(params[key])}`;
    });

    return url;
  }

  async get(endpoint: string, params: Record<string, any> = {}): Promise<any> {
    const url = this.buildUrl(endpoint, params);
    console.log(`[Server] WooCommerce API GET request: ${url}`); // Added [Server] for clarity

    try {
      const response = await fetch(url);

      if (!response.ok) {
        console.error(`[Server] WooCommerce API error: ${response.status} ${response.statusText}`);
        throw new Error(`WooCommerce API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      // console.log(`[Server] WooCommerce API response for ${endpoint}:`, data); // Reduced logging noise
      return data;
    } catch (error) {
      console.error(`[Server] WooCommerce API GET request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  async post(endpoint: string, data: Record<string, any>): Promise<any> {
    const url = this.buildUrl(endpoint);
    console.log(`[Server] WooCommerce API POST request: ${url}`);
    // console.log('[Server] Request data:', data); // Reduced logging noise

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[Server] WooCommerce API POST error: ${response.status} ${response.statusText}`);
        console.error('[Server] Error details:', errorText);
        throw new Error(`WooCommerce API error: ${response.status} ${response.statusText}`);
      }

      const responseData = await response.json();
      // console.log(`[Server] WooCommerce API response for ${endpoint}:`, responseData); // Reduced logging noise
      return responseData;
    } catch (error) {
      console.error(`[Server] WooCommerce API POST request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Method specifically for PUT requests if needed later
  async put(endpoint: string, data: Record<string, any>): Promise<any> {
    const url = this.buildUrl(endpoint);
    console.log(`[Server] WooCommerce API PUT request: ${url}`);

    try {
        const response = await fetch(url, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[Server] WooCommerce API PUT error: ${response.status} ${response.statusText}`);
            console.error('[Server] Error details:', errorText);
            throw new Error(`WooCommerce API PUT error: ${response.status} ${response.statusText}`);
        }

        const responseData = await response.json();
        return responseData;
    } catch (error) {
        console.error(`[Server] WooCommerce API PUT request failed for ${endpoint}:`, error);
        throw error;
    }
  }
}

// Initialize the WooCommerce API client (Server-Side Only)
const api = new WooCommerceClient();

// Service functions (Server-Side Only)
export const wooCommerceService = {
  // Get base URL
  get baseUrl() {
    return env.WOOCOMMERCE_URL || '';
  },

  // Get consumer key
  get consumerKey() {
    return env.WOOCOMMERCE_CONSUMER_KEY || '';
  },

  get consumerSecret() {
    return env.WOOCOMMERCE_CONSUMER_SECRET || '';
  },

  /**
   * Get all products
   */
  async getProducts(): Promise<Product[]> {
    try {
      return await api.get('products');
    } catch (error) {
      console.error('[Server] Error fetching products:', error);
      return [];
    }
  },

  /**
   * Get a single product by ID
   */
  async getProduct(id: number): Promise<Product | null> {
    try {
      return await api.get(`products/${id}`);
    } catch (error) {
      console.error(`[Server] Error fetching product ${id}:`, error);
      return null;
    }
  },

  /**
   * Get product variations
   */
  async getProductVariations(productId: number): Promise<ProductVariation[]> {
    try {
      return await api.get(`products/${productId}/variations`);
    } catch (error) {
      console.error(`[Server] Error fetching variations for product ${productId}:`, error);
      return [];
    }
  },

  /**
   * Get categories
   */
  async getCategories(): Promise<ProductCategory[]> {
    try {
      return await api.get('products/categories');
    } catch (error) {
      console.error('[Server] Error fetching categories:', error);
      return [];
    }
  },

  /**
   * Get an order by ID
   */
  async getOrder(id: number): Promise<Order | null> {
    try {
      return await api.get(`orders/${id}`);
    } catch (error) {
      console.error(`[Server] Error fetching order ${id}:`, error);
      return null;
    }
  },

  /**
   * Update an order
   */
  async updateOrder(id: number, data: Partial<Order>): Promise<Order | null> {
    try {
      return await api.put(`orders/${id}`, data);
    } catch (error) {
      console.error(`[Server] Error updating order ${id}:`, error);
      return null;
    }
  },

  /**
   * Create a new order
   */
  async createOrder(orderData: {
    customer_note?: string;
    billing: OrderBilling;
    line_items: OrderLineItem[];
    meta_data?: OrderMetaData[];
  }): Promise<Order | null> {
    try {
      // Set default status to processing
      const data = {
        ...orderData,
        status: 'processing',
      };

      const response = await api.post('orders', data);
      return response;
    } catch (error) {
      console.error('[Server] Error creating order:', error);
      return null;
    }
  },

  // Removed uploadFile function as it was insecure and exposed API credentials to client
};
