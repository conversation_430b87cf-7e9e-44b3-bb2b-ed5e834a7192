import { PrismaClient } from '@prisma/client';
import { dev, building } from '$app/environment'; // Add 'building'
import { env as privateEnv } from '$env/dynamic/private'; // Import $env/dynamic/private

// Removed old top-level console.log statements for DATABASE_URL


// Define the PaymentVerification type (can be inferred from Prisma, but good for reference)
// Note: Prisma generates its own types, which should ideally be used.
export interface PaymentVerification {
  id: number;
  transaction_id: string;
  order_id: number;
  payment_service?: string | null; // Match Prisma schema (nullable)
  verification_date: Date; // Use Date object
  image_path?: string | null; // Match Prisma schema (nullable)
  verification_status: string;
  created_at: Date; // Use Date object
  updated_at: Date; // Use Date object
}

// Prisma Client Initialization (Singleton pattern)
let prisma: PrismaClient;

if (building) {
  // During SvelteKit build phase, $env/dynamic/private might be undefined or empty.
  // Prisma Client should not be initialized here if it relies on runtime env vars.
  console.log('[db.ts] App is BUILDING. Prisma Client initialization deferred to runtime.');
  // Provide a mock Prisma Client to prevent errors if db functions are somehow called during build.
  // This mock will throw if any actual DB operation is attempted during build.
  prisma = new Proxy(
    {},
    {
      get(target, prop) {
        // Allow $connect and $disconnect for type compatibility if something tries to call them.
        if (prop === '$connect' || prop === '$disconnect') {
          return async () => {
            console.warn(`PrismaClient.${String(prop)} called during build. Mocked.`);
          };
        }
        // For any other property access, throw an error.
        throw new Error(
          `PrismaClient method '${String(prop)}' attempted during build phase. This is not allowed.`
        );
      }
    }
  ) as unknown as PrismaClient;
} else {
  // Runtime (dev server or production server)
  console.log(`[db.ts] Runtime: DATABASE_URL from $env/dynamic/private: ${privateEnv.DATABASE_URL}`);
  console.log(`[db.ts] Runtime: DATABASE_URL from process.env: ${process.env.DATABASE_URL}`);

  if (!privateEnv.DATABASE_URL && !process.env.DATABASE_URL) {
    console.error(
      '[db.ts] CRITICAL: DATABASE_URL is not set at runtime. Prisma Client may fail to initialize.'
    );
    // Consider throwing an error here if DATABASE_URL is absolutely required for the app to start.
    // throw new Error("DATABASE_URL environment variable is not set.");
  }

  // Standard Prisma Client initialization for runtime
  if (dev) {
    // In development, use a global variable to preserve the client across HMR reloads
    // @ts-ignore
    if (!global._prisma) {
      // @ts-ignore
      global._prisma = new PrismaClient();
    }
    // @ts-ignore
    prisma = global._prisma;
  } else {
    // In production, always create a new instance
    prisma = new PrismaClient();
  }
}

export { prisma }; // Export the prisma instance (either real or mock)

// --- Functions using Prisma Client ---

// Find a transaction by transaction_id
export const findTransactionById = async (transactionId: string): Promise<PaymentVerification | null> => {
  try {
    const transaction = await prisma.paymentVerification.findUnique({
      where: { transaction_id: transactionId },
    });
    return transaction;
  } catch (error) {
    console.error(`Error finding transaction by ID ${transactionId}:`, error);
    return null; // Return null on error
  }
};

// Add a new transaction
// Input type should match Prisma's generated `PaymentVerificationCreateInput` ideally,
// but using a simplified version for now.
export const addTransaction = async (transactionData: {
  transaction_id: string;
  order_id: number;
  payment_service?: string | null;
  image_path?: string | null;
  verification_status?: string; // Allow optional status, defaults in schema
}): Promise<PaymentVerification | null> => {
  try {
    const newTransaction = await prisma.paymentVerification.create({
      data: {
        transaction_id: transactionData.transaction_id,
        order_id: transactionData.order_id,
        payment_service: transactionData.payment_service,
        image_path: transactionData.image_path,
        // verification_date is handled by @default(now()) in schema
        // created_at is handled by @default(now()) in schema
        // updated_at is handled by @updatedAt in schema
        // verification_status uses @default("verified") if not provided
        ...(transactionData.verification_status && { verification_status: transactionData.verification_status }),
      },
    });
    return newTransaction;
  } catch (error) {
    console.error('Error adding transaction:', error);
    // Handle potential unique constraint violation specifically if needed
    // e.g., if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') { ... }
    return null; // Return null on error
  }
};

// Get all transactions with pagination and search
export const getAllTransactions = async (options?: {
  page?: number;
  limit?: number;
  search?: string;
}): Promise<PaymentVerification[]> => {
    try {
        const { page = 1, limit = 20, search = '' } = options || {};
        const skip = (page - 1) * limit;

        // Build where clause for search
        const where = search ? {
            OR: [
                { transaction_id: { contains: search } },
                { payment_service: { contains: search } }
            ]
        } : {};

        const transactions = await prisma.paymentVerification.findMany({
            where,
            orderBy: { created_at: 'desc' },
            skip,
            take: limit
        });
        return transactions;
    } catch (error) {
        console.error('Error fetching all transactions:', error);
        return [];
    }
}

// Delete a transaction by ID
export const deleteTransaction = async (id: number): Promise<boolean> => {
    try {
        await prisma.paymentVerification.delete({
            where: { id }
        });
        return true;
    } catch (error) {
        console.error(`Error deleting transaction with ID ${id}:`, error);
        return false;
    }
}

// Get transaction count for pagination
export const getTransactionCount = async (search?: string): Promise<number> => {
    try {
        const where = search ? {
            OR: [
                { transaction_id: { contains: search } },
                { payment_service: { contains: search } }
            ]
        } : {};

        const count = await prisma.paymentVerification.count({ where });
        return count;
    } catch (error) {
        console.error('Error counting transactions:', error);
        return 0;
    }
}

// Note: The original JSON functions (loadTransactions, saveTransactions, ensureDirectoryExists) are removed.

// Update verification order ID after order creation
export const updateVerificationOrderId = async (
  verificationId: number,
  orderId: number
): Promise<boolean> => {
  try {
    await prisma.paymentVerification.update({
      where: { id: verificationId },
      data: { order_id: orderId }
    });
    console.log(`Updated verification ${verificationId} with order ID ${orderId}`);
    return true;
  } catch (error) {
    console.error(`Error updating verification order ID:`, error);
    return false;
  }
};
