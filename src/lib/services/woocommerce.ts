// Define types for WooCommerce products and related entities
// These types can be shared between server and client code.

export interface Product {
  id: number;
  name: string;
  slug: string;
  permalink: string;
  date_created: string;
  description: string;
  short_description: string;
  price: string;
  regular_price: string;
  sale_price: string;
  on_sale: boolean;
  status: string;
  featured: boolean;
  catalog_visibility: string;
  images: ProductImage[];
  categories: ProductCategory[];
  tags: ProductTag[];
  attributes: ProductAttribute[];
  type: string;
  variations?: number[];
  price_html?: string;
}

// Order interfaces
export interface Order {
  id: number;
  number: string;
  status: string;
  date_created: string;
  total: string;
  customer_id: number;
  billing: OrderBilling;
  shipping: OrderShipping;
  line_items: OrderLineItem[];
  meta_data: OrderMetaData[];
}

export interface OrderBilling {
  first_name: string;
  last_name?: string;
  company?: string;
  address_1?: string;
  address_2?: string;
  city?: string;
  state?: string;
  postcode?: string;
  country?: string;
  email?: string;
  phone: string;
}

export interface OrderShipping {
  first_name?: string;
  last_name?: string;
  company?: string;
  address_1?: string;
  address_2?: string;
  city?: string;
  state?: string;
  postcode?: string;
  country?: string;
}

export interface OrderLineItem {
  product_id: number;
  variation_id?: number;
  quantity: number;
  name?: string;
  price?: string;
}

export interface OrderMetaData {
  key: string;
  value: string;
}

export interface ProductImage {
  id: number;
  date_created: string;
  date_modified: string;
  src: string;
  name: string;
  alt: string;
}

export interface ProductCategory {
  id: number;
  name: string;
  slug: string;
}

export interface ProductTag {
  id: number;
  name: string;
  slug: string;
}

export interface ProductAttribute {
  id: number;
  name: string;
  position: number;
  visible: boolean;
  variation: boolean;
  options: string[];
}

export interface ProductVariation {
  id: number;
  date_created: string;
  description: string;
  permalink: string;
  sku: string;
  price: string;
  regular_price: string;
  sale_price: string;
  on_sale: boolean;
  status: string;
  purchasable: boolean;
  virtual: boolean;
  downloadable: boolean;
  attributes: VariationAttribute[];
  image: ProductImage;
}

export interface VariationAttribute {
  id: number;
  name: string;
  option: string;
}
