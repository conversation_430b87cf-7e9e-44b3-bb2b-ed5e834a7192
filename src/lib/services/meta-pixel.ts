// Meta Pixel (Facebook Pixel) Configuration
import { env } from '$env/dynamic/public'; // Import public env var

// Event names
export enum MetaPixelEvent {
  PAGE_VIEW = 'PageView',
  VIEW_CONTENT = 'ViewContent',
  ADD_TO_CART = 'AddToCart',
  INITIATE_CHECKOUT = 'InitiateCheckout',
  ADD_PAYMENT_INFO = 'AddPaymentInfo',
  LEAD = 'Lead',
  COMPLETE_REGISTRATION = 'CompleteRegistration',
  CONTACT = 'Contact',
  CUSTOMIZE_PRODUCT = 'CustomizeProduct',
  DONATE = 'Donate',
  FIND_LOCATION = 'FindLocation',
  SCHEDULE = 'Schedule',
  START_TRIAL = 'StartTrial',
  SUBMIT_APPLICATION = 'SubmitApplication',
  SUBSCRIBE = 'Subscribe',
  SEARCH = 'Search'
}

// Flag to track if pixel has been initialized
let pixelInitialized = false;

// Get or create a persistent external ID for the user
export function getExternalId(): string {
  if (typeof window === 'undefined') {
    return 'ext_' + Math.random().toString(36).substring(2, 15); // Fallback for SSR
  }

  // Check if we already have an external ID in localStorage
  const storedId = localStorage.getItem('meta_external_id');

  if (storedId) {
    return storedId; // Return existing ID if available
  }

  // Generate a new ID if none exists
  const newId = 'ext_' + Math.random().toString(36).substring(2, 15) + '_' + Date.now().toString(36);

  // Store the new ID in localStorage for persistence
  try {
    localStorage.setItem('meta_external_id', newId);

    // Also set as a cookie for cross-page persistence
    document.cookie = `meta_external_id=${newId}; path=/; max-age=${60*60*24*365*2}`; // 2 years

    console.log('Generated new persistent external ID:', newId);
  } catch (e) {
    console.error('Failed to store external ID:', e);
  }

  return newId;
}

// Initialize tracking identifiers early
export function initTrackingIds(): void {
  if (typeof window === 'undefined') return;

  // Get or create the external ID
  const externalId = getExternalId();
  console.log('Initialized tracking with external ID:', externalId);

  // Check Facebook cookies
  checkFacebookCookies();
}

// Initialize Meta Pixel
export function initMetaPixel(): void {
  if (typeof window === 'undefined') return;

  // Initialize tracking IDs first
  initTrackingIds();

  // Prevent multiple initializations
  if (pixelInitialized) {
    console.log('Meta Pixel already initialized');
    return;
  }

  // Create and inject the Facebook Pixel script
  const script = document.createElement('script');
  script.async = true;
  script.src = 'https://connect.facebook.net/en_US/fbevents.js';
  
  // Initialize pixel after script loads
  script.onload = () => {
    if (window.fbq && env.PUBLIC_META_PIXEL_ID) {
      window.fbq('init', env.PUBLIC_META_PIXEL_ID);
      pixelInitialized = true;
      console.log('Meta Pixel initialized with ID:', env.PUBLIC_META_PIXEL_ID);
      
      // Track initial PageView with enhanced parameters
      const fbp = getCookie('_fbp');
      const fbc = getCookie('_fbc');
      const externalId = getExternalId();
      
      const pageViewParams: Record<string, any> = {
        page_title: document.title,
        page_location: window.location.href,
        page_path: window.location.pathname,
        external_id: externalId,
        content_type: 'page',
        content_name: document.title || 'Home Page',
        event_url: window.location.href
      };
      
      if (fbp) pageViewParams.fbp = fbp;
      if (fbc) pageViewParams.fbc = fbc;
      
      // Track with browser pixel
      window.fbq('track', 'PageView', pageViewParams);
      
      // Send to Conversion API
      sendToConversionAPI(MetaPixelEvent.PAGE_VIEW, pageViewParams);
      
      console.log('Initial PageView tracked with external_id:', externalId);
    }
  };

  // Add error handling
  script.onerror = () => {
    console.error('Failed to load Facebook Pixel script');
  };

  // Append script to document head
  const firstScript = document.getElementsByTagName('script')[0];
  if (firstScript && firstScript.parentNode) {
    firstScript.parentNode.insertBefore(script, firstScript);
  } else {
    document.head.appendChild(script);
  }

  // Add noscript fallback
  const noscript = document.createElement('noscript');
  const img = document.createElement('img');
  img.height = 1;
  img.width = 1;
  img.style.display = 'none';
  img.src = `https://www.facebook.com/tr?id=${env.PUBLIC_META_PIXEL_ID}&ev=PageView&noscript=1`;
  noscript.appendChild(img);
  document.head.appendChild(noscript);

  // Initialize the fbq function queue
  if (!window.fbq) {
    window.fbq = function() {
      window.fbq.callMethod ?
        window.fbq.callMethod.apply(window.fbq, arguments) :
        window.fbq.queue.push(arguments);
    };
    if (!window._fbq) window._fbq = window.fbq;
    window.fbq.push = window.fbq;
    window.fbq.loaded = true;
    window.fbq.version = '2.0';
    window.fbq.queue = [];
  }
}

// Track an event with the Meta Pixel
export function trackEvent(eventName: MetaPixelEvent, eventParams?: Record<string, any>): void {
  if (typeof window === 'undefined') return;

  // Make sure pixel is initialized
  if (!pixelInitialized) {
    initMetaPixel();
  }

  // Get Facebook cookies
  const fbp = getCookie('_fbp');
  const fbc = getCookie('_fbc');
  const externalId = getExternalId();

  // Create a new params object with all necessary tracking data for browser pixel
  // Moved this block earlier so browserParams is available for queuing
  const browserParams: Record<string, any> = {
    ...eventParams,
    external_id: eventParams?.external_id || externalId,  // This is critical for browser pixel events
    event_url: window.location.href,
    page_title: document.title
  };

  // Add Facebook cookies if available
  if (fbp) browserParams.fbp = fbp;
  if (fbc) browserParams.fbc = fbc;

  // Create a separate object for Conversion API with structure expected by our server endpoint
  // Our server endpoint expects _fbp, _fbc, and external_id directly in eventParams
  const conversionApiParams: Record<string, any> = {
    ...eventParams, // Start with original eventParams
    external_id: eventParams?.external_id || externalId, // Ensure external_id is included
    _fbp: fbp, // Add fbp directly
    _fbc: fbc, // Add fbc directly
    event_url: window.location.href, // Add standard params
    page_title: document.title,
  };

  // Always send to Conversion API via our server endpoint
  sendToConversionAPI(eventName, conversionApiParams);

  // Handle browser pixel tracking (call or queue)
  if (window.fbq) {
    // fbq is ready, track immediately
    try {
      console.log(`Tracking Meta Pixel event (Browser - Immediate): ${eventName}`, browserParams);
      window.fbq('track', eventName, browserParams);
    } catch (error) {
      console.error('Error tracking Meta Pixel event (Browser - Immediate):', error);
    }
  } else {
    // fbq is not ready, push to the queue
    console.warn(`Meta Pixel not fully loaded, queuing browser event: ${eventName}`);
    // Use fbq.push as defined in the initialization snippet
    if (typeof window.fbq === 'function' && typeof window.fbq.push === 'function') {
       window.fbq.push(['track', eventName, browserParams]);
    } else {
       console.error('Meta Pixel queue (fbq.push) not available.');
    }
  }
}

// Helper function to get cookie value by name
function getCookie(name: string): string | null {
  if (typeof document === 'undefined') return null;

  const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
  return match ? match[2] : null;
}

// Send event to Conversion API via our server endpoint
async function sendToConversionAPI(eventName: MetaPixelEvent, eventParams: Record<string, any>): Promise<void> {
  // eventParams here is the object prepared in trackEvent,
  // which should now include external_id, _fbp, _fbc, etc.

  // Log the tracking parameters for debugging (excluding token)
  console.log('Conversion API tracking parameters (sent to local endpoint):', {
    eventName,
    eventParams, // Log the full eventParams object being sent
    // Note: Token is NOT sent from client
  });

  try {
    const response = await fetch('/api/meta-conversion', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        // Send necessary data to the server endpoint
        eventName,
        eventParams: eventParams, // Send the prepared eventParams object
        url: window.location.href,
        userAgent: navigator.userAgent,
        referrer: document.referrer,
        eventTime: Math.floor(Date.now() / 1000),
        // Do NOT include the Conversion API token here
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to send to Conversion API: ${response.status}`);
    }

    console.log('Event sent to Conversion API successfully');
  } catch (error) {
    console.error('Error sending to Conversion API:', error);
  }
}

// Function to manually trigger a PageView event
export function trackPageView(additionalParams?: Record<string, any>): void {
  if (typeof window === 'undefined') return;

  // Make sure pixel is initialized
  if (!pixelInitialized) {
    initMetaPixel();
    return; // initMetaPixel will trigger a PageView
  }

  // Get tracking identifiers
  const fbp = getCookie('_fbp');
  const fbc = getCookie('_fbc');
  const externalId = getExternalId();

  // Create enhanced parameters with all important tracking data
  // For browser pixel events, external_id must be directly in the params object
  const pageViewParams: Record<string, any> = {
    page_title: document.title,
    page_location: window.location.href,
    page_path: window.location.pathname,
    external_id: externalId,  // This is critical for browser pixel events
    content_type: 'page',
    content_name: document.title || 'Page',
    event_url: window.location.href,
    ...additionalParams
  };

  // Add Facebook cookies if available
  if (fbp) pageViewParams.fbp = fbp;
  if (fbc) pageViewParams.fbc = fbc;

  // For Conversion API, we need to structure the data differently
  const conversionApiParams = {
    ...pageViewParams,
    user_data: {
      external_id: externalId
    }
  };

  // Send to Conversion API
  sendToConversionAPI(MetaPixelEvent.PAGE_VIEW, conversionApiParams);

  console.log('Manual PageView event tracked with external_id:', externalId);
}

// Function to check if Facebook cookies are set
export function checkFacebookCookies(): { fbp: string | null, fbc: string | null, externalId: string | null } {
  if (typeof document === 'undefined') return { fbp: null, fbc: null, externalId: null };

  const fbp = getCookie('_fbp');
  const fbc = getCookie('_fbc');
  const storedExternalId = localStorage.getItem('meta_external_id');
  const cookieExternalId = getCookie('meta_external_id');

  console.log('Facebook tracking status:', {
    fbp: fbp ? 'Set' : 'Not set',
    fbc: fbc ? 'Set' : 'Not set',
    externalId: storedExternalId ? 'Set' : 'Not set',
    fbpValue: fbp,
    fbcValue: fbc,
    localStorageExternalId: storedExternalId,
    cookieExternalId: cookieExternalId,
    consistent: storedExternalId === cookieExternalId ? 'Yes' : 'No'
  });

  return { fbp, fbc, externalId: storedExternalId };
}

// Add TypeScript declaration for fbq
declare global {
  interface Window {
    fbq: any;
    _fbq: any;
  }
}
