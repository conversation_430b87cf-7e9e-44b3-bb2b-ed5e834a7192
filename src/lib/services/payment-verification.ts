// Payment verification service
import type { PaymentVerificationResult } from '$lib/types';
import { fileToBase64 } from '$lib/utils'; // Import from shared utilities

/**
 * Verify a payment proof image
 */
export async function verifyPaymentProof(
  imageFile: File,
  orderId: number,
  actualFileName?: string
): Promise<PaymentVerificationResult> {
  try {
    const imageBase64 = await fileToBase64(imageFile);
    const response = await fetch('/api/payment-verification/verify', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        imageBase64,
        orderId,
        fileName: actualFileName || imageFile.name // Use actual filename if provided
      }),
    });

    // Detailed error handling based on response
    if (!response.ok) {
      let errorResult: PaymentVerificationResult = {
        success: false,
        error: `Payment verification failed with HTTP status ${response.status}`,
        status: 'ai_error', // Default status for HTTP errors
      };
      try {
        // Attempt to parse a JSON error response from the server
        const apiError = await response.json();
        errorResult.error = apiError.error || errorResult.error;
        errorResult.status = apiError.status || errorResult.status;
        errorResult.debug_error = apiError.debug_error;
        errorResult.data = apiError.data;
        errorResult.transactionId = apiError.transactionId;
        errorResult.paymentService = apiError.paymentService;
      } catch (e) {
        // If the error response isn't JSON, or parsing fails
        console.error('Failed to parse error response from verification API:', e);
      }
      console.error('Payment verification API error:', errorResult);
      return errorResult;
    }

    return await response.json();
  } catch (error: any) {
    console.error('Client-side error in verifyPaymentProof:', error);
    return {
      success: false,
      error: error.message || 'A client-side error occurred during payment verification.',
      status: 'ai_error', // Generic status for unexpected client-side errors
    };
  }
}

/**
 * Check if a transaction ID already exists in the database
 */
export async function checkDuplicateTransactionId(transactionId: string): Promise<boolean> {
  try {
    const response = await fetch('/api/payment-verification/check-duplicate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ transactionId })
    });
    
    const result = await response.json();
    return result.isDuplicate;
  } catch (error) {
    console.error('Error checking for duplicate transaction:', error);
    throw error;
  }
}
