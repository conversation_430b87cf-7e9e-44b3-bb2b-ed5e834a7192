import { trackEvent, MetaPixelEvent, getExternalId } from '$lib/services/meta-pixel';
import type { Product, ProductVariation } from '$lib/services/woocommerce';
import { TRACKING_CONFIG } from '$lib/config/constants';

export function trackProductView(product: Product) {
  trackEvent(MetaPixelEvent.VIEW_CONTENT, {
    content_type: 'product',
    content_ids: [product.id.toString()],
    content_name: product.name,
    content_category: product.categories && Array.isArray(product.categories) 
      ? product.categories.map((cat: any) => cat.name).join(', ') 
      : TRACKING_CONFIG.CONTENT_CATEGORIES.PRODUCT,
    value: product.price ? parseFloat(product.price) : 0,
    currency: TRACKING_CONFIG.CURRENCY
  });
}

export function trackPageView(pageInfo: {
  content_name: string;
  content_category?: string;
  page_title?: string;
}) {
  trackEvent(MetaPixelEvent.VIEW_CONTENT, {
    content_type: 'page',
    content_name: pageInfo.content_name,
    content_category: pageInfo.content_category || TRACKING_CONFIG.CONTENT_CATEGORIES.HOME,
    page_title: pageInfo.page_title || document.title
  });
}

export function trackInitiateCheckout(params: {
  product: Product;
  variation?: ProductVariation;
  price: number;
}) {
  const { product, variation, price } = params;
  
  trackEvent(MetaPixelEvent.INITIATE_CHECKOUT, {
    content_type: 'product',
    content_ids: [product.id.toString()],
    content_name: product.name,
    value: price,
    currency: TRACKING_CONFIG.CURRENCY,
    contents: [
      {
        id: variation ? variation.id.toString() : product.id.toString(),
        quantity: 1,
        item_price: price
      }
    ],
    external_id: getExternalId()
  });
}

export function trackAddPaymentInfo(params: {
  product: Product;
  variation?: ProductVariation;
  price: number;
}) {
  const { product, variation, price } = params;
  
  trackEvent(MetaPixelEvent.ADD_PAYMENT_INFO, {
    content_type: 'product',
    content_ids: [product.id.toString()],
    content_name: product.name,
    value: price,
    currency: TRACKING_CONFIG.CURRENCY,
    contents: [
      {
        id: variation ? variation.id.toString() : product.id.toString(),
        quantity: 1,
        item_price: price
      }
    ],
    external_id: getExternalId()
  });
}

export function trackLead(params: {
  product: Product;
  variation?: ProductVariation;
  price: number;
}) {
  const { product, variation, price } = params;
  
  trackEvent(MetaPixelEvent.LEAD, {
    content_type: 'product',
    content_ids: [product.id.toString()],
    content_name: product.name,
    value: price,
    currency: TRACKING_CONFIG.CURRENCY,
    contents: [
      {
        id: variation ? variation.id.toString() : product.id.toString(),
        quantity: 1,
        item_price: price
      }
    ],
    external_id: getExternalId()
  });
}

export function getProductPrice(product: Product, variation?: ProductVariation): number {
  if (variation) {
    return variation.on_sale 
      ? parseFloat(variation.sale_price) 
      : parseFloat(variation.regular_price);
  }
  
  return product.on_sale 
    ? parseFloat(product.sale_price) 
    : parseFloat(product.regular_price);
} 