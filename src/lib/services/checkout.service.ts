import type { Product, ProductVariation } from '$lib/services/woocommerce';
import type { PaymentVerificationResult } from '$lib/types';
import { verifyPaymentProof } from '$lib/services/payment-verification';
import { trackEvent, MetaPixelEvent, getExternalId } from '$lib/services/meta-pixel';
import { API_ENDPOINTS, ERROR_MESSAGES, PAYMENT, PRODUCTS } from '$lib/config/constants';

export interface CheckoutData {
  billing: {
    first_name: string;
    phone: string;
  };
  line_items: Array<{
    product_id: number;
    variation_id?: number;
    quantity: number;
    meta_data?: Array<{
      key: string;
      value: string;
    }>;
  }>;
  customer_note: string;
  meta_data: Array<{
    key: string;
    value: string;
  }>;
}

export interface CheckoutParams {
  product: Product;
  paymentProofFile: File;
  customerData: {
    name: string;
    phone: string;
  };
  variation?: ProductVariation;
  subscriptionUrl?: string; // For bandwidth upgrade
}

export interface CheckoutResult {
  success: boolean;
  orderId?: number;
  orderNumber?: string;
  error?: string;
}

export class CheckoutService {
  private getExternalIdWithCookies() {
    const externalId = getExternalId();
    const fbp = document.cookie.match(/_fbp=([^;]+)/)?.[1] || '';
    const fbc = document.cookie.match(/_fbc=([^;]+)/)?.[1] || '';
    
    return { externalId, fbp, fbc };
  }

  private async uploadFile(file: File): Promise<{ success: boolean; fileUrl?: string; error?: string }> {
    const formData = new FormData();
    formData.append('file', file);

    console.log('Uploading file to server...');

    const uploadResponse = await fetch(API_ENDPOINTS.UPLOAD_PAYMENT_PROOF, {
      method: 'POST',
      body: formData
    });

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      console.error('File upload failed:', uploadResponse.status, uploadResponse.statusText);
      console.error('Error details:', errorText);
      throw new Error(`File upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
    }

    const result = await uploadResponse.json();
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to upload payment proof');
    }

    return result;
  }

  private async verifyPayment(file: File, productId: number, filename: string): Promise<PaymentVerificationResult> {
    const verificationResult = await verifyPaymentProof(file, productId, filename);

    // Handle AI verification bypass scenario
    if (verificationResult.status === 'ai_verification_bypassed') {
      console.warn('AI verification was bypassed for product ID:', productId, 'Message:', verificationResult.message);
    } else if (!verificationResult.success) {
      // Handle different verification failure scenarios
      switch (verificationResult.status) {
        case 'invalid_receipt':
          throw new Error(ERROR_MESSAGES.INVALID_RECEIPT);
        case 'duplicate_transaction':
          throw new Error(ERROR_MESSAGES.DUPLICATE_TRANSACTION);
        default:
          throw new Error(ERROR_MESSAGES.GENERIC_ERROR);
      }
    }

    return verificationResult;
  }

  private buildOrderData(params: CheckoutParams, verificationResult: PaymentVerificationResult, fileUrl: string): CheckoutData {
    const { product, customerData, variation, subscriptionUrl } = params;
    const { externalId, fbp, fbc } = this.getExternalIdWithCookies();
    
    const fullFileUrl = fileUrl.startsWith('http') ? fileUrl : window.location.origin + fileUrl;
    
    const orderData: CheckoutData = {
      billing: {
        first_name: customerData.name || 'Customer',
        phone: customerData.phone || PAYMENT.DEFAULT_PHONE
      },
      line_items: [
        {
          product_id: product.id,
          variation_id: variation?.id,
          quantity: 1,
          ...(subscriptionUrl && {
            meta_data: [
              {
                key: 'Subscription Url',
                value: subscriptionUrl
              }
            ]
          })
        }
      ],
      customer_note: `Payment proof uploaded: ${params.paymentProofFile.name}\nPayment proof URL: ${fullFileUrl}`,
      meta_data: [
        {
          key: 'ss',
          value: fullFileUrl
        },
        {
          key: 'payment_proof_filename',
          value: params.paymentProofFile.name
        },
        {
          key: 'payment_proof_filetype',
          value: params.paymentProofFile.type
        },
        {
          key: 'payment_proof_filesize',
          value: params.paymentProofFile.size.toString()
        },
        {
          key: 'transaction_id',
          value: verificationResult.transactionId || ''
        },
        {
          key: 'payment_service',
          value: verificationResult.paymentService || 'Unknown'
        },
        {
          key: 'external_id',
          value: externalId
        },
        {
          key: 'fbp',
          value: fbp
        },
        {
          key: 'fbc',
          value: fbc
        },
        {
          key: 'event_source_url',
          value: window.location.href
        },
        {
          key: 'user_agent',
          value: navigator.userAgent
        }
      ]
    };

    return orderData;
  }

  private async createOrder(orderData: CheckoutData, verificationId: number): Promise<{ success: boolean; order?: any; error?: string }> {
    const response = await fetch(API_ENDPOINTS.CREATE_ORDER, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ orderData, verificationId })
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to create order');
    }

    return result;
  }

  private trackEvents(params: CheckoutParams, orderId: number, orderPrice: number) {
    const { product, variation } = params;
    const { externalId } = this.getExternalIdWithCookies();

    // Track AddPaymentInfo event
    trackEvent(MetaPixelEvent.ADD_PAYMENT_INFO, {
      content_type: 'product',
      content_ids: [product.id.toString()],
      content_name: product.name,
      value: orderPrice,
      currency: 'MMK',
      contents: [
        {
          id: variation ? variation.id.toString() : product.id.toString(),
          quantity: 1,
          item_price: orderPrice
        }
      ],
      external_id: externalId
    });

    // Track Lead event
    trackEvent(MetaPixelEvent.LEAD, {
      content_type: 'product',
      content_ids: [product.id.toString()],
      content_name: product.name,
      value: orderPrice,
      currency: 'MMK',
      contents: [
        {
          id: variation ? variation.id.toString() : product.id.toString(),
          quantity: 1,
          item_price: orderPrice
        }
      ],
      external_id: externalId
    });
  }

  public async processCheckout(params: CheckoutParams): Promise<CheckoutResult> {
    try {
      const { product, paymentProofFile, variation } = params;

      // 1. Upload payment proof
      const uploadResult = await this.uploadFile(paymentProofFile);
      console.log('File uploaded successfully:', uploadResult);

      // 2. Verify payment proof
      const actualFilename = uploadResult.fileUrl?.split('/').pop() || paymentProofFile.name;
      const productId = product.id === PRODUCTS.BANDWIDTH_UPGRADE_ID ? PRODUCTS.BANDWIDTH_UPGRADE_ID : product.id;
      const verificationResult = await this.verifyPayment(paymentProofFile, productId, actualFilename);
      console.log('Payment verified successfully:', verificationResult);

      if (!verificationResult.verificationId) {
        throw new Error('Verification ID was not returned from the verification step.');
      }

      // 3. Build order data
      const orderData = this.buildOrderData(params, verificationResult, uploadResult.fileUrl!);

      // 4. Create order and link verification in one server-side step
      const orderResult = await this.createOrder(orderData, verificationResult.verificationId);
      console.log('Order created and linked:', orderResult);

      // 6. Track events
      const orderPrice = variation
        ? (variation.on_sale ? parseFloat(variation.sale_price) : parseFloat(variation.regular_price))
        : (product?.on_sale ? parseFloat(product.sale_price) : parseFloat(product.regular_price));

      this.trackEvents(params, orderResult.order.id, orderPrice);

      return {
        success: true,
        orderId: orderResult.order.id,
        orderNumber: orderResult.order.number
      };

    } catch (error) {
      console.error('Checkout error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : ERROR_MESSAGES.GENERIC_ERROR
      };
    }
  }
}

// Export singleton instance
export const checkoutService = new CheckoutService(); 