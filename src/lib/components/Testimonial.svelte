<script lang="ts">
  import Typography from './Typography.svelte';
  import { typography } from '$lib/styles/typography';

  export let name: string;
  export let image: string = "/images/avatar.png"; // Default avatar
  export let rating: number = 5; // Default 5 stars
</script>

<div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-300 flex flex-col h-full">
  <div class="flex items-center gap-4 mb-4">
    <img src={image} alt={name} class="w-14 h-14 rounded-full object-cover border-2 border-primary/20" />
    <div>
      <Typography variant="h4">{name}</Typography>
      <div class="flex">
        {#each Array(rating) as _, i}
          <span class="text-amber-400">★</span>
        {/each}
        {#each Array(5 - rating) as _, i}
          <span class="text-gray-300">★</span>
        {/each}
      </div>
    </div>
  </div>

  <div class="relative">
    <span class="text-5xl text-primary/10 absolute -top-4 -left-2">"</span>
    <div class="relative z-10 pl-4">
      <div class="{typography.paragraph.base}">
        <slot />
      </div>
    </div>
    <span class="text-5xl text-primary/10 absolute bottom-0 right-0">"</span>
  </div>
</div>
