<script lang="ts">
  import { cn } from "$lib/utils";
  import { cva, type VariantProps } from "class-variance-authority";
  // No need for these imports
  // import { getContext, onMount, setContext } from "svelte";
  // import { writable, type Writable } from "svelte/store";

  const progressVariants = cva(
    "relative h-2 w-full overflow-hidden rounded-full bg-primary/20",
    {
      variants: {
        variant: {
          default: "bg-primary/20",
          success: "bg-success/20",
          destructive: "bg-destructive/20",
        },
      },
      defaultVariants: {
        variant: "default",
      },
    }
  );

  const indicatorVariants = cva("h-full w-full flex-1 transition-all", {
    variants: {
      variant: {
        default: "bg-primary",
        success: "bg-success",
        destructive: "bg-destructive",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  });

  interface ProgressProps extends VariantProps<typeof progressVariants> {
    value?: number;
    max?: number;
    getValueLabel?: (value: number, max: number) => string;
  }

  type $$Props = ProgressProps & { [key: string]: any };

  let className: string | undefined | null = undefined;
  export { className as class };
  export let value: number | undefined = undefined;
  export let max: number = 100;
  export let variant: VariantProps<typeof progressVariants>["variant"] = "default";
  export let getValueLabel: (value: number, max: number) => string = (value, max) =>
    `${Math.round((value / max) * 100)}%`;

  let indicatorStyle: string;
  let progressValue: number = 0;

  $: {
    progressValue = value || 0;
    indicatorStyle = `transform: translateX(-${100 - ((progressValue / max) * 100)}%)`;
  }
</script>

<div
  role="progressbar"
  aria-valuemin={0}
  aria-valuemax={max}
  aria-valuenow={progressValue}
  aria-valuetext={getValueLabel(progressValue, max)}
  data-state={progressValue === 0 ? "indeterminate" : "loading"}
  data-value={progressValue}
  data-max={max}
  class={cn(progressVariants({ variant }), className)}
  {...$$restProps}
>
  <div
    class={cn(indicatorVariants({ variant }))}
    style={indicatorStyle}
  ></div>
</div>
