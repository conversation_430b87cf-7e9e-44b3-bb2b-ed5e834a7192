<script lang="ts">
  import type { ProductVariation, Product } from '$lib/services/woocommerce';
  import { trackEvent, MetaPixelEvent, getExternalId } from '$lib/services/meta-pixel';
  import { verifyPaymentProof } from '$lib/services/payment-verification';
  import { checkoutService } from '$lib/services/checkout.service';
  import { FILE_UPLOAD, ERROR_MESSAGES, PAYMENT } from '$lib/config/constants';

  // Props
  const { product, selectedVariation, success, error, cancel } = $props<{
    product: Product;
    selectedVariation: ProductVariation | null;
    success: (event: { orderId: number; orderNumber: string }) => void;
    error: (event: { message: string }) => void;
    cancel: () => void;
  }>();

  // Form state
  let customerName = $state('');
  let customerPhone = $state('');
  let paymentProofFile: File | null = $state(null);
  let isSubmitting = $state(false);
  let formError = $state('');
  let fileError = $state('');

  // Tracking data
  let externalId = $state(getExternalId());
  let fbp = $state('');
  let fbc = $state('');

  // File input reference
  let fileInput: HTMLInputElement;

  // Handle file selection
  function handleFileChange(event: Event) {
    const input = event.target as HTMLInputElement;
    const files = input.files;

    if (!files || files.length === 0) {
      paymentProofFile = null;
      fileError = '';
      return;
    }

    const file = files[0];

    // Check file type
    if (!FILE_UPLOAD.ALLOWED_TYPES.includes(file.type as any)) {
      fileError = ERROR_MESSAGES.INVALID_FILE_TYPE;
      paymentProofFile = null;
      return;
    }

    // Check file size
    if (file.size > FILE_UPLOAD.MAX_SIZE_BYTES) {
      fileError = ERROR_MESSAGES.FILE_TOO_LARGE;
      paymentProofFile = null;
      return;
    }

    paymentProofFile = file;
    fileError = '';
  }

  // Reset file input
  function resetFileInput() {
    if (fileInput) {
      fileInput.value = '';
    }
    paymentProofFile = null;
    fileError = '';
  }

  // Submit form using the new checkout service
  async function submitOrder() {
    // Reset errors
    formError = '';

    // Validate form
    if (!customerName.trim()) {
      formError = ERROR_MESSAGES.NAME_REQUIRED;
      return;
    }

    if (!customerPhone.trim()) {
      formError = ERROR_MESSAGES.PHONE_REQUIRED;
      return;
    }

    if (!paymentProofFile) {
      formError = ERROR_MESSAGES.PAYMENT_PROOF_REQUIRED;
      return;
    }

    // Validate selected variation for variable products
    if (product.type === 'variable' && !selectedVariation) {
      formError = 'Please select product options';
      return;
    }

    try {
      isSubmitting = true;

      // Use the checkout service
      const result = await checkoutService.processCheckout({
        product,
        paymentProofFile,
        customerData: {
          name: customerName,
          phone: customerPhone
        },
        variation: selectedVariation
      });

      if (result.success) {
        // Dispatch success event
        if (typeof success === 'function') {
          success({
            orderId: result.orderId!,
            orderNumber: result.orderNumber!
          });
        }

        // Reset form
        customerName = '';
        customerPhone = '';
        resetFileInput();
      } else {
        throw new Error(result.error || ERROR_MESSAGES.GENERIC_ERROR);
      }

    } catch (error) {
      console.error('Error submitting order:', error);
      formError = error instanceof Error ? error.message : ERROR_MESSAGES.GENERIC_ERROR;
      if (typeof error === 'function') error({ message: formError });
    } finally {
      isSubmitting = false;
    }
  }

  // Cancel checkout
  function cancelCheckout() {
    if (typeof cancel === 'function') {
      cancel();
    }
  }
</script>

<div class="bg-white p-6 rounded-lg shadow-lg">
  <h2 class="text-2xl font-heading font-bold text-secondary mb-6">Checkout Form</h2>

  <!-- Product Summary -->
  <div class="mb-6 p-4 bg-accent/30 rounded-md">
    <h3 class="font-heading text-2xl font-bold text-secondary mb-2">Order အချက်အလက်</h3>
    <div class="flex justify-between items-center">
      <div>
        <p class="font-medium">{product.name}</p>
        {#if selectedVariation}
          <p class="text-sm text-secondary/70">
            {selectedVariation.attributes.map((attr: any) => attr.option).join(' / ')}
          </p>
        {/if}
      </div>
      <div class="text-right">
        {#if selectedVariation}
          {#if selectedVariation.on_sale}
            <p class="font-bold text-primary">{selectedVariation.sale_price} Ks</p>
            <p class="text-sm text-secondary/50 line-through">{selectedVariation.regular_price} Ks</p>
          {:else}
            <p class="font-bold text-primary">{selectedVariation.regular_price} Ks</p>
          {/if}
        {:else}
          <p class="font-bold text-primary">{product.price} Ks</p>
        {/if}
      </div>
    </div>
  </div>

  <!-- Checkout Form -->
  <form class="space-y-4" onsubmit={(e) => { e.preventDefault(); submitOrder(); }}>
    <!-- Name Field -->
    <div>
      <label for="customer-name" class="block text-secondary font-medium mb-1">နာမည် *</label>
      <input
        type="text"
        id="customer-name"
        bind:value={customerName}
        class="w-full px-4 py-2 border border-accent rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
        placeholder="Enter your full name"
        required
      />
    </div>

    <!-- Phone Field -->
    <div>
      <label for="customer-phone" class="block text-secondary font-medium mb-1">ဖုန်း *</label>
      <input
        type="tel"
        id="customer-phone"
        bind:value={customerPhone}
        class="w-full px-4 py-2 border border-accent rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
        placeholder="Enter your phone number"
        required
      />
    </div>

    <!-- Payment Information -->
    <div class="mb-4 p-4 bg-blue-50 rounded-md border border-blue-100">
      <h4 class="text-2xl font-heading font-bold text-secondary mb-2">ငွေလွှဲရန်-</h4>
      <div class="space-y-3">
        <div class="flex items-center gap-2">
          <div class="bg-primary text-white p-1 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <span class="font-medium">{PAYMENT.SERVICES.join(' / ')}</span>
        </div>
        <div class="ml-6">
          <p class="text-secondary">နာမည်- <span class="font-medium">{PAYMENT.RECEIVER_NAME}</span></p>
          <p class="text-secondary">ဖုန်း- <span class="font-medium">{PAYMENT.RECEIVER_PHONE}</span></p>
          <p class="text-xs text-secondary/70 mt-1">* ဖုန်းခေါ်ဆိုမှုများ လက်ခံမည်မဟုတ်ပါ။ ငွေလွှဲရန်သာ အသုံးပြုပါ။</p>
          <p class="text-xs text-red-500 mt-1">* ငွေလွှဲမှတ်ချက်တွင် "IPမန်း , IPman, VPN" အစရှိသောစကားလုံးများ "မရေး" ပါနှင့်</p>
        </div>
      </div>
    </div>

    <!-- Payment Proof Upload -->
    <div>
      <label for="payment-proof" class="block text-secondary font-medium mb-1">ငွေလွှဲပြေစာ *</label>
      <div class="flex flex-col space-y-2">
        <input
          type="file"
          id="payment-proof"
          accept="image/*"
          bind:this={fileInput}
          onchange={handleFileChange}
          class="hidden"
        />
        <div class="flex items-center space-x-2">
          <button
            type="button"
            onclick={() => fileInput.click()}
            class="px-4 py-2 bg-accent text-secondary font-medium rounded-md hover:bg-accent/80 transition-colors duration-300"
          >
            SS ပုံထည့်မည်။
          </button>
          {#if paymentProofFile}
            <span class="text-sm text-secondary/70">{paymentProofFile.name}</span>
            <button
              type="button"
              onclick={resetFileInput}
              class="text-red-500 hover:text-red-700"
              aria-label="Remove file"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </button>
          {:else}
            <span class="text-sm text-secondary/70">No file chosen</span>
          {/if}
        </div>
        {#if fileError}
          <p class="text-sm text-red-500">{fileError}</p>
        {:else}
          <p class="text-xs text-secondary/50">ငွေလွှဲပြေစာ SS ထည့်ပေးပါ။</p>
        {/if}
      </div>
    </div>

    <!-- Error Message -->
    {#if formError}
      <div class="p-3 bg-red-100 text-red-700 rounded-md">
        {formError}
      </div>
    {/if}

    <!-- Form Actions -->
    <div class="flex space-x-4 pt-4">
      <button
        type="button"
        onclick={cancelCheckout}
        class="px-6 py-2 border border-accent text-secondary font-medium rounded-md hover:bg-accent/20 transition-colors duration-300"
        disabled={isSubmitting}
      >
        ထွက်မည်
      </button>
      <button
        type="submit"
        class="flex-1 px-6 py-2 bg-primary text-white font-medium rounded-md hover:bg-primary/90 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={isSubmitting}
      >
        {#if isSubmitting}
          <span class="flex items-center justify-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            ခဏစောင့်ပါ...
          </span>
        {:else}
          Order တင်မည်
        {/if}
      </button>
    </div>
  </form>
</div>
