<script lang="ts">
  import { onMount } from 'svelte';
  // We don't need to import the logo, we'll use the static path

  // State for mobile menu
  let isMenuOpen = $state(false);

  // Function to toggle mobile menu
  function toggleMenu() {
    isMenuOpen = !isMenuOpen;

    // Prevent body scrolling when menu is open
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  }

  // Close mobile menu when clicking outside
  function closeMenu() {
    isMenuOpen = false;
    document.body.style.overflow = '';
  }

  // Escape key handler for accessibility
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      closeMenu();
    }
  }

  // Track scroll position for header styling
  let scrollY = $state(0);
  let innerWidth = $state(0);

  onMount(() => {
    const handleScroll = () => {
      scrollY = window.scrollY;
    };

    const handleResize = () => {
      innerWidth = window.innerWidth;
      if (innerWidth >= 768 && isMenuOpen) {
        closeMenu();
      }
    };

    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleResize);

    handleResize();

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  });
</script>

<header
  class="fixed w-full z-50 transition-all duration-300 bg-white/20 backdrop-blur-xs"
  class:shadow-md={scrollY > 20}
  class:py-2={scrollY > 20}
  class:py-4={scrollY <= 20}
  style="transform: translateY({scrollY > 500 && scrollY < 1000 ? '-100%' : '0'}); opacity: {scrollY > 20 ? 0.98 : 1}; background-color: {scrollY > 20 ? 'rgba(255, 255, 255, 0.8)' : 'rgba(255, 255, 255, 0)'};"
>
  <div class="container mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center">
    <!-- Logo -->
    <a href="/" class="flex items-center group transition-transform duration-300 hover:scale-105">
      <div class="relative overflow-hidden rounded-md">
        <img src="/images/Logo.svg" alt="IPman VPN" class=" h-15 relative z-10" />
        <div class="absolute inset-0 bg-primary/10 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-500 rounded-md"></div>
      </div>
      <!-- <span class="ml-2 text-xl font-bold font-heading text-secondary group-hover:text-primary transition-colors duration-300">IPman</span> -->
    </a>

    <!-- Desktop Navigation -->
    <nav class="hidden md:flex items-center space-x-8">
      <a href="/" class="font-body text-secondary hover:text-primary transition-colors duration-300 relative group">
        Home
        <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
      </a>
      <a href="/plans" class="font-body text-secondary hover:text-primary transition-colors duration-300 relative group">
        Plans
        <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
      </a>
      <a href="/policy" class="font-body text-secondary hover:text-primary transition-colors duration-300 relative group">
        Policy
        <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
      </a>
      <a
        href="/download"
        class="px-5 py-2 bg-primary text-white font-heading rounded-md hover:bg-opacity-90 transition-all duration-300 shadow-sm hover:shadow-md"
      >
        Download
      </a>
    </nav>

    <!-- Mobile Menu Button -->
    <button
      class="md:hidden flex items-center p-2 text-secondary"
      onclick={toggleMenu}
      aria-label="Toggle menu"
    >
      <svg
        class=" w-8 "
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        {#if isMenuOpen}
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        {:else}
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 6h16M4 12h16M4 18h16"
          />
        {/if}
      </svg>
    </button>
  </div>


</header>

<!-- Mobile Menu (outside header for proper positioning) -->
<div
  class="fixed inset-0 backdrop-blur-sm bg-secondary/30 z-40 transition-all duration-500"
  class:opacity-100={isMenuOpen}
  class:opacity-0={!isMenuOpen}
  class:visible={isMenuOpen}
  class:invisible={!isMenuOpen}
  class:pointer-events-auto={isMenuOpen}
  class:pointer-events-none={!isMenuOpen}
  style="z-index: 998;"
  onclick={closeMenu}
  onkeydown={handleKeydown}
  role="button"
  tabindex="0"
  aria-label="Close menu overlay"
></div>
<div
  class="fixed right-0 top-0 bottom-0 w-72 bg-accent shadow-xl transition-all duration-500 transform ease-out"
  style="z-index: 999;"
  class:translate-x-0={isMenuOpen}
  class:translate-x-full={!isMenuOpen}
  class:visible={isMenuOpen}
  class:invisible={!isMenuOpen}
>
  <div class="p-6 h-full flex flex-col">
    <div class="flex justify-between items-center mb-8 opacity-0 transition-all duration-300"
      class:opacity-100={isMenuOpen}
      style="transition-delay: 100ms;"
    >
      <span class="text-xl font-bold font-heading text-secondary">IPman</span>
      <button
        onclick={closeMenu}
        class="text-secondary hover:text-primary transition-colors duration-300 p-1 rounded-full hover:bg-accent/50"
        aria-label="Close menu"
      >
        <svg
          class="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>
    <nav class="flex flex-col space-y-4 flex-grow">
      <a
        href="/"
        class="py-2 font-body text-secondary hover:text-primary transition-all duration-300 border-b border-primary/20 relative overflow-hidden group opacity-0 translate-x-8"
        class:opacity-100={isMenuOpen}
        class:translate-x-0={isMenuOpen}
        style="transition-delay: 150ms;"
        onclick={closeMenu}
      >
        <span class="relative z-10">Home</span>
        <span class="absolute inset-0 bg-primary/10 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-300"></span>
      </a>
      <a
        href="/plans"
        class="py-2 font-body text-secondary hover:text-primary transition-all duration-300 border-b border-primary/20 relative overflow-hidden group opacity-0 translate-x-8"
        class:opacity-100={isMenuOpen}
        class:translate-x-0={isMenuOpen}
        style="transition-delay: 200ms;"
        onclick={closeMenu}
      >
        <span class="relative z-10">Plans</span>
        <span class="absolute inset-0 bg-primary/10 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-300"></span>
      </a>
      <a
        href="/policy"
        class="py-2 font-body text-secondary hover:text-primary transition-all duration-300 border-b border-primary/20 relative overflow-hidden group opacity-0 translate-x-8"
        class:opacity-100={isMenuOpen}
        class:translate-x-0={isMenuOpen}
        style="transition-delay: 250ms;"
        onclick={closeMenu}
      >
        <span class="relative z-10">Policy</span>
        <span class="absolute inset-0 bg-primary/10 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-300"></span>
      </a>

      <div class="mt-auto pt-6 opacity-0 translate-y-4 transition-all duration-500"
        class:opacity-100={isMenuOpen}
        class:translate-y-0={isMenuOpen}
        style="transition-delay: 400ms;"
      >
        <a
          href="/download"
          class="block w-full py-3 bg-primary text-white font-heading rounded-md hover:bg-opacity-90 transition-all duration-300 shadow-sm hover:shadow-md text-center"
        >
          Download
        </a>
      </div>
    </nav>
  </div>
</div>