<script lang="ts">
  // Props
  const { orderId, orderNumber, close, productId } = $props<{
    orderId: number;
    orderNumber: string;
    close: () => void;
    productId : number;
  }>();

  // Close confirmation
  function closeConfirmation() {
    if (typeof close === 'function') {
      close();
    }
  }
</script>

<div class="bg-white p-6 rounded-lg shadow-lg text-center">
  <div class="mb-6">
    <div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
      </svg>
    </div>
  </div>

  <h2 class="text-2xl font-heading font-bold text-secondary mb-2">အော်ဒါတင်ခြင်း အောင်မြင်ပါသည်။</h2>
  <p class="text-secondary/70 mb-6">ကျေးဇူးတင်ပါတယ်။</p>

  <div class="bg-accent/30 p-4 rounded-md mb-6">
    <p class="font-medium">Order အမှတ်: <span class="font-bold">{orderNumber || 'N/A'}</span></p>
    <p class="text-sm text-secondary/70">ဤအော်ဒါ အမှတ်စဉ်အား SS ရိုက်ယူ၍ဖြစ်စေ / Note ထဲတွင်ဖြစ်စေ ရေးမှတ်ထားပေးပါ။</p>
  </div>



  {#if productId !== 1001}

  <p class="mb-6 text-secondary/70">
    IPမန်း VPN လင့်ဝယ်ယူရန် အော်ဒါဖောင်တင်ခြင်း အောင်မြင်ပါတယ်။ ဝယ်ယူထားသောလင့်အားထုတ်ယူရန် အောက်မှ "လင့်ယူမည်" ခလုတ်အားနှိပ်ပေးပါ။
  </p>

  <p class="mb-6 text-red-600">
    မှတ်ချက်- လင့်ယူမည် နှိပ်ရန်အတွက် Telegram ရှိရန်လိုအပ်ပါသည်။
  </p>

  <div class="flex flex-col items-center space-y-4 mb-6">
    <a
      href={`https://t.me/ipman_user_bot?start=${orderId}`}
      class="inline-flex items-center justify-center px-8 py-4 w-full max-w-xs bg-primary text-white font-heading rounded-md hover:bg-primary-dark hover:-translate-y-1 hover:shadow-lg transition-all duration-300 shadow-md text-lg md:text-xl relative overflow-hidden group"
    >
      <span class="relative z-10 inline-flex items-center">
        <!-- Telegram SVG icon -->
        <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 fill-current mr-2" viewBox="0 0 24 24">
          <path d="M12 0C5.372 0 .001 5.372.001 12S5.372 24 12 24s12-5.372 12-12S18.628 0 12 0zm5.51 7.07l-1.874 8.845c-.142.64-.515.795-1.04.494l-2.877-2.123-1.387 1.335c-.153.153-.28.28-.572.28l.204-2.896 5.263-4.754c.228-.204-.05-.318-.353-.114l-6.497 4.072-2.797-.874c-.608-.19-.62-.608.127-.9L16.5 7.07c.576-.19 1.08.14.999.916z"/>
        </svg>
        <span>လင့်ယူမည်</span>
      </span>
      <span class="absolute inset-0 bg-gradient-to-r from-primary-dark to-primary scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
    </a>

    <button
      type="button"
      onclick={closeConfirmation}
      class="px-6 py-2 w-full max-w-xs bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700 transition-colors duration-300"
    >
      ထွက်မည်
    </button>
  </div>
  {:else}

    <p class="mb-6 text-secondary/70">
      GB တိုးအော်ဒါဖောင်တင်ခြင်းအောင်မြင်ပါတယ်။ စစ်ဆေးအတည်ပြုပြီးပါက Telegram မှ အကြောင်းကြားစာရောက်ရှိလာပါလိမ့်မယ်။ ( မနက်-9 / ည-9 အတွင်းသာ )
    </p>

    <button
      type="button"
      onclick={closeConfirmation}
      class="px-6 py-2 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700 transition-colors duration-300"
    >
      ထွက်မည်
    </button>
  {/if}
</div>
