<script lang="ts">
  import Typography from './Typography.svelte';
  
  export let title: string;
  export let subtitle: string = '';
  export let centered: boolean = false;
  export let className: string = '';
</script>

<div class={`mb-12 ${centered ? 'text-center' : ''} ${className}`}>
  <Typography variant="h2" className="mb-4">{title}</Typography>
  {#if subtitle}
    <Typography variant="lead" className="max-w-3xl {centered ? 'mx-auto' : ''}">{subtitle}</Typography>
  {/if}
</div>
