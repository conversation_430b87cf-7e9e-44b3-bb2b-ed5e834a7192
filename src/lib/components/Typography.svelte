<script lang="ts">
  import { typography } from '$lib/styles/typography';
  
  // Props
  export let variant: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'lead' | 'small' | 'tiny' = 'p';
  export let color: string = ''; // Optional custom color class
  export let className: string = ''; // Additional classes
  export let as: string = ''; // Optional element override
  
  // Determine the element to render
  let element: string;
  if (as) {
    element = as;
  } else if (variant.startsWith('h')) {
    element = variant;
  } else {
    element = 'p';
  }
  
  // Determine the classes to apply
  let classes: string;
  if (variant.startsWith('h')) {
    classes = typography.heading[variant as keyof typeof typography.heading];
  } else if (variant === 'p') {
    classes = typography.paragraph.base;
  } else if (variant === 'lead') {
    classes = typography.paragraph.lead;
  } else if (variant === 'small') {
    classes = typography.paragraph.small;
  } else if (variant === 'tiny') {
    classes = typography.paragraph.tiny;
  } else {
    classes = typography.paragraph.base;
  }
  
  // Add custom color if provided
  if (color) {
    classes = classes.replace(/text-[a-z0-9/-]+/g, color);
  }
  
  // Add additional classes
  if (className) {
    classes = `${classes} ${className}`;
  }
</script>

{#if element === 'h1'}
  <h1 class={classes}><slot /></h1>
{:else if element === 'h2'}
  <h2 class={classes}><slot /></h2>
{:else if element === 'h3'}
  <h3 class={classes}><slot /></h3>
{:else if element === 'h4'}
  <h4 class={classes}><slot /></h4>
{:else if element === 'h5'}
  <h5 class={classes}><slot /></h5>
{:else if element === 'h6'}
  <h6 class={classes}><slot /></h6>
{:else if element === 'span'}
  <span class={classes}><slot /></span>
{:else if element === 'div'}
  <div class={classes}><slot /></div>
{:else}
  <p class={classes}><slot /></p>
{/if}
