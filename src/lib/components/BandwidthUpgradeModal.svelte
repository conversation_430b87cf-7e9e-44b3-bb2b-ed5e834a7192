<script lang="ts">
  import { onMount } from 'svelte';
  import type { ProductVariation } from '$lib/services/woocommerce';
  // Removed incorrect import: import { wooCommerceService } from '$lib/services/woocommerce';
  import OrderConfirmation from '$lib/components/OrderConfirmation.svelte';
  import { getExternalId } from '$lib/services/meta-pixel';
  import { trackEvent, MetaPixelEvent } from '$lib/services/meta-pixel';
  import { verifyPaymentProof } from '$lib/services/payment-verification';
  import { setUrlParams } from '$lib/utils/url';
  import { checkoutService } from '$lib/services/checkout.service';
  import { PRODUCTS, FILE_UPLOAD, ERROR_MESSAGES, PAYMENT } from '$lib/config/constants';

  // Props
  const { subscriptionUrl, userName, onClose, onSuccess, initialOrderId, initialOrderNumber, initialSuccess } = $props<{
    subscriptionUrl: string;
    userName: string | null;
    onClose: () => void;
    onSuccess: (event: { orderId: number; orderNumber: string }) => void;
    initialOrderId?: number | null;
    initialOrderNumber?: string;
    initialSuccess?: boolean;
  }>();

  // State
  let variations = $state<ProductVariation[]>([]);
  let selectedVariation = $state<number | null>(null);
  let paymentProofFile = $state<File | null>(null);
  let paymentProofPreview = $state<string | null>(null);
  let isSubmitting = $state(false);
  let isLoading = $state(true);
  let checkoutError = $state<string | null>(null);
  let checkoutSuccess = $state(initialSuccess || false);
  let fileError = $state('');
  let orderId = $state<number | null>(initialOrderId || null);
  let orderNumber = $state<string>(initialOrderNumber || '');

  // File input reference
  let fileInput = $state<HTMLInputElement | null>(null);

  // Fetch product variations on mount
  onMount(async () => {
    try {
      isLoading = true;
      const productId = 1001; // Bandwidth upgrade product ID

      // Fetch all variations for product ID 1001 using the new API endpoint
      const variationsResponse = await fetch(`/api/woocommerce/products/${productId}/variations`);
      
      if (!variationsResponse.ok) {
        let errorDetails = '';
        try {
          const errorData = await variationsResponse.json();
          errorDetails = errorData.error || JSON.stringify(errorData);
        } catch (e) {
          // If parsing errorData as JSON fails, try to get raw text
          errorDetails = await variationsResponse.text();
        }
        throw new Error(`Failed to fetch product variations: ${variationsResponse.status} ${variationsResponse.statusText}. Details: ${errorDetails}`);
      }
      
      const fetchedVariationsData = await variationsResponse.json();

      if (Array.isArray(fetchedVariationsData) && fetchedVariationsData.length > 0) {
        variations = fetchedVariationsData as ProductVariation[];
        console.log('Fetched all variations at once:', variations);
      } else {
        console.error('No variations fetched or an empty array was returned from API. Using fallback data.');
        // Provide fallback variation data if API fails or returns no variations
        variations = [
          {
            id: 1002, price: "5800", attributes: [{ id: 1, name: "GB", option: "5 GB" }],
            date_created: "", description: "", permalink: "", sku: "", regular_price: "5800", sale_price: "5800", on_sale: false
          } as ProductVariation,
          {
            id: 1003, price: "9800", attributes: [{ id: 1, name: "GB", option: "10 GB" }],
            date_created: "", description: "", permalink: "", sku: "", regular_price: "9800", sale_price: "9800", on_sale: false
          } as ProductVariation,
          {
            id: 1004, price: "14800", attributes: [{ id: 1, name: "GB", option: "15 GB" }],
            date_created: "", description: "", permalink: "", sku: "", regular_price: "14800", sale_price: "14800", on_sale: false
          } as ProductVariation,
          {
            id: 1005, price: "19800", attributes: [{ id: 1, name: "GB", option: "20 GB" }],
            date_created: "", description: "", permalink: "", sku: "", regular_price: "19800", sale_price: "19800", on_sale: false
          } as ProductVariation,
          {
            id: 1006, price: "29800", attributes: [{ id: 1, name: "GB", option: "30 GB" }],
            date_created: "", description: "", permalink: "", sku: "", regular_price: "29800", sale_price: "29800", on_sale: false
          } as ProductVariation
        ];
      }

      // Track ViewContent event for bandwidth upgrade
      trackEvent(MetaPixelEvent.VIEW_CONTENT, {
        content_type: 'product',
        content_ids: ['1001'], // Product ID for Bandwidth Upgrade
        content_name: 'Bandwidth Upgrade',
        content_category: 'VPN Services',
        currency: 'MMK'
        // value: 0 // Optional: if you want to track a value for ViewContent
      });
    } catch (error) {
      console.error('Error fetching variations:', error);
      // Use a more user-friendly error message, potentially from the error object if it's structured
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred.';
      checkoutError = `Failed to load product options: ${errorMessage}. Please try again.`;
      
      // Fallback to default variations if API fails catastrophically and variations array is still empty
      if (!variations || variations.length === 0) {
         console.warn('Using hardcoded fallback variations due to error and no variations loaded:', error);
         variations = [
            {
              id: 1002, price: "5800", attributes: [{ id: 1, name: "GB", option: "5 GB" }],
              date_created: "", description: "", permalink: "", sku: "", regular_price: "5800", sale_price: "5800", on_sale: false
            } as ProductVariation,
            {
              id: 1003, price: "9800", attributes: [{ id: 1, name: "GB", option: "10 GB" }],
              date_created: "", description: "", permalink: "", sku: "", regular_price: "9800", sale_price: "9800", on_sale: false
            } as ProductVariation,
            {
              id: 1004, price: "14800", attributes: [{ id: 1, name: "GB", option: "15 GB" }],
              date_created: "", description: "", permalink: "", sku: "", regular_price: "14800", sale_price: "14800", on_sale: false
            } as ProductVariation,
            {
              id: 1005, price: "19800", attributes: [{ id: 1, name: "GB", option: "20 GB" }],
              date_created: "", description: "", permalink: "", sku: "", regular_price: "19800", sale_price: "19800", on_sale: false
            } as ProductVariation,
            {
              id: 1006, price: "29800", attributes: [{ id: 1, name: "GB", option: "30 GB" }],
              date_created: "", description: "", permalink: "", sku: "", regular_price: "29800", sale_price: "29800", on_sale: false
            } as ProductVariation
         ];
      }
    } finally {
      isLoading = false;
    }
  });

  // Handle file selection
  function handleFileChange(event: Event) {
    const input = event.target as HTMLInputElement;
    const files = input.files;

    if (!files || files.length === 0) {
      paymentProofFile = null;
      paymentProofPreview = null;
      fileError = '';
      return;
    }

    const file = files[0];

    // Check file type
    if (!FILE_UPLOAD.ALLOWED_TYPES.includes(file.type as any)) {
      fileError = ERROR_MESSAGES.INVALID_FILE_TYPE;
      paymentProofFile = null;
      paymentProofPreview = null;
      return;
    }

    // Check file size
    if (file.size > FILE_UPLOAD.MAX_SIZE_BYTES) {
      fileError = ERROR_MESSAGES.FILE_TOO_LARGE;
      paymentProofFile = null;
      paymentProofPreview = null;
      return;
    }

    paymentProofFile = file;
    fileError = '';

    // Create a preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      paymentProofPreview = e.target?.result as string;
    };
    reader.readAsDataURL(file);
  }

  // Reset file input
  function resetFileInput() {
    if (fileInput) {
      fileInput.value = '';
    }
    paymentProofFile = null;
    paymentProofPreview = null;
    fileError = '';
  }

  // Reset checkout form
  function resetCheckoutForm() {
    selectedVariation = null;
    paymentProofFile = null;
    paymentProofPreview = null;
    isSubmitting = false;
    checkoutError = null;
    fileError = '';
  }

  // Close checkout modal
  function closeModal() {
    if (!isSubmitting) {
      if (!checkoutSuccess) {
        resetCheckoutForm();
      }

      // Update URL parameters to remove modal
      setUrlParams({
        'modal': null,
        'success': null,
        'order_id': null,
        'order_number': null
      });

      onClose();
    }
  }

  // Submit order to WooCommerce using the new checkout service
  async function submitOrder() {
    if (!selectedVariation || !paymentProofFile || !subscriptionUrl) {
      checkoutError = 'Please select a GB option and upload payment proof';
      return;
    }

    isSubmitting = true;
    checkoutError = null;

    try {
      // Create a product object for the bandwidth upgrade
      const bandwidthProduct = {
        id: PRODUCTS.BANDWIDTH_UPGRADE_ID,
        name: 'Bandwidth Upgrade',
        slug: 'bandwidth-upgrade',
        type: 'variable' as const,
        price: '',
        regular_price: '',
        sale_price: '',
        on_sale: false,
        status: 'publish',
        featured: false,
        catalog_visibility: 'visible',
        description: '',
        short_description: '',
        sku: '',
        permalink: '',
        date_created: '',
        images: [],
        categories: [],
        tags: [],
        attributes: [],
        variations: []
      };

      // Find the selected variation
      const variation = variations.find(v => v.id === selectedVariation);
      
      // Use the checkout service
      const result = await checkoutService.processCheckout({
        product: bandwidthProduct,
        paymentProofFile,
        customerData: {
          name: userName || 'Customer',
          phone: PAYMENT.DEFAULT_PHONE
        },
        variation,
        subscriptionUrl
      });

      if (result.success) {
        orderId = result.orderId!;
        orderNumber = result.orderNumber!;
        checkoutSuccess = true;

        // Update URL parameters to reflect success state
        setUrlParams({
          'success': 'true',
          'order_id': result.orderId!.toString(),
          'order_number': result.orderNumber!
        });

        if (onSuccess) onSuccess({
          orderId: result.orderId!,
          orderNumber: result.orderNumber!
        });
      } else {
        throw new Error(result.error || ERROR_MESSAGES.GENERIC_ERROR);
      }

    } catch (error) {
      console.error('Error creating order:', error);
      checkoutError = error instanceof Error ? error.message : ERROR_MESSAGES.GENERIC_ERROR;
    } finally {
      isSubmitting = false;
    }
  }

  // Format price in MMK
  function formatPrice(price: string): string {
    return parseInt(price).toLocaleString() + ' MMK';
  }
</script>

<div class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
    <!-- Modal Header -->
    <div class="p-4 border-b flex justify-between items-center">
      <h3 class="text-3xl font-medium">
        {#if checkoutSuccess}
          Order Successful
        {:else}
          GB တိုးမည်
        {/if}
      </h3>
      <button
        class="text-gray-500 hover:text-gray-700"
        onclick={closeModal}
        aria-label="Close modal"
        disabled={isSubmitting}
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Modal Content -->
    <div class="p-6">
      {#if checkoutSuccess}
        <!-- Order Confirmation -->
        <OrderConfirmation
          orderId={orderId || 0}
          orderNumber={orderNumber}
          close={closeModal}
          productId={1001}
        />
      {:else if isLoading}
        <!-- Loading State -->
        <div class="flex justify-center items-center py-8">
          <svg class="animate-spin h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      {:else}
        <!-- Checkout Form -->
        {#if checkoutError}
          <div class="mb-4 p-4 bg-red-50 border-l-4 border-red-500 text-red-700">
            <p>{checkoutError}</p>
          </div>
        {/if}

        <div class="space-y-4">
          <!-- GB Options -->
          <div>
            <p class="block text-sm font-medium text-gray-700 mb-2">GB ပမာဏ ရွေးချယ်ပါ</p>
            <div class="grid grid-cols-2 gap-2">
              {#each variations as variation}
                <button
                  class="p-3 border rounded-md text-left {selectedVariation === variation.id ? 'border-primary bg-primary/10' : 'border-gray-300 hover:border-gray-400'}"
                  onclick={() => selectedVariation = variation.id}
                >
                  <div class="font-medium">{variation.attributes?.[0]?.option || 'Option'}</div>
                  <div class="text-gray-600">{formatPrice(variation.price)}</div>
                </button>
              {/each}
            </div>
          </div>

          <!-- Payment Information -->
          <div class="p-4 bg-blue-50 rounded-md border border-blue-100 my-4">
            <h4 class="text-2xl font-medium text-gray-800 mb-2 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              ငွေလွှဲရန်-
            </h4>
            <div class="space-y-2 ml-5">
              <p class="text-gray-700 text-sm">{PAYMENT.SERVICES.join(' / ')}</p>
              <p class="text-gray-700 text-sm">နာမည်- <span class="font-medium">{PAYMENT.RECEIVER_NAME}</span></p>
              <p class="text-gray-700 text-sm">ဖုန်း- <span class="font-medium">{PAYMENT.RECEIVER_PHONE}</span></p>
              <p class="text-xs text-gray-500 mt-1">* ဖုန်းခေါ်ဆိုမှုများ လက်ခံမည်မဟုတ်ပါ။ ငွေလွှဲရန်သာ အသုံးပြုပါ။</p>
              <p class="text-xs text-red-500 mt-1">* ငွေလွှဲမှတ်ချက်တွင် "IPမန်း , IPman, VPN" အစရှိသောစကားလုံးများ "မရေး" ပါနှင့်</p>
            </div>
          </div>

          <!-- Payment Proof Upload -->
          <div>
            <p class="block text-sm font-medium text-gray-700 mb-2">ငွေလွှဲပြေစာ SS ထည့်ပါ</p>
            <input
              type="file"
              id="payment-proof"
              accept="image/*"
              class="sr-only"
              bind:this={fileInput}
              onchange={handleFileChange}
            />
            <div class="flex items-center space-x-2">
              <button
                type="button"
                onclick={() => fileInput?.click()}
                class="px-4 py-2 bg-primary text-white font-medium rounded-md hover:bg-primary/80 transition-colors duration-300"
              >
                SS ထည့်ရန်
              </button>
              {#if paymentProofFile}
                <span class="text-sm text-gray-500">{paymentProofFile.name}</span>
                <button
                  type="button"
                  onclick={resetFileInput}
                  class="text-red-500 hover:text-red-700"
                  aria-label="Remove file"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                  </svg>
                </button>
              {:else}
                <span class="text-sm text-gray-500">No file chosen</span>
              {/if}
            </div>

            {#if paymentProofPreview}
              <div class="mt-3 border rounded-md p-2 max-w-xs mx-auto">
                <img src={paymentProofPreview} alt="Payment proof preview" class="mx-auto h-32 object-contain" />
              </div>
            {/if}

            {#if fileError}
              <p class="mt-1 text-sm text-red-600">{fileError}</p>
            {/if}
          </div>

          <!-- Submit Button -->
          <button
            class="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-md font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none {isSubmitting || !selectedVariation || !paymentProofFile ? 'opacity-50 cursor-not-allowed' : ''}"
            onclick={submitOrder}
            disabled={isSubmitting || !selectedVariation || !paymentProofFile}
          >
            {#if isSubmitting}
              <span class="flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </span>
            {:else}
              အော်ဒါတင်မည်
            {/if}
          </button>
        </div>
      {/if}
    </div>
  </div>
</div>
