<script lang="ts">
  import type { Product } from '$lib/services/woocommerce';
  import { typography } from '$lib/styles/typography';
  import Typography from './Typography.svelte';

  export let product: Product;
  export let isGolden: boolean = false;
  export let className: string = '';

  // Helper function to format price
  function formatPrice(price: string): string {
    if (!price) return '';
    return parseInt(price).toLocaleString() + ' Ks';
  }
</script>

<div class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105 hover:shadow-xl border-t-8 {isGolden ? 'border-amber-500' : 'border-primary'} {className}">
  <!-- Plan Header -->
  <div class="{isGolden ? 'bg-gradient-to-r from-amber-500 to-amber-400' : 'bg-gradient-to-r from-primary to-primary/90'} p-8 text-white">
    <div class="flex items-center justify-between mb-4">
      <Typography variant="h3" className="text-white text-4xl md:text-5xl">{product.name}</Typography>
      {#if isGolden}
        <span class="bg-white text-amber-500 px-3 py-1 rounded-full text-sm font-bold">RECOMMENDED</span>
      {/if}
    </div>
    <div class="flex items-center">
      <span class="{typography.special.priceLarge} text-white text-3xl md:text-4xl">{formatPrice(product.price)}</span>
      {#if product.on_sale && product.regular_price}
        <span class="ml-2 line-through text-white/70">{formatPrice(product.regular_price)}</span>
      {/if}
    </div>
  </div>

  <!-- Plan Content -->
  <div class="p-8 flex-1 flex flex-col">
    <div class="mb-6">
      <div class="{typography.paragraph.base} prose prose-lg">
        {@html product.short_description}
      </div>
    </div>

    <div class="space-y-4 mb-8 flex-1">
      <!-- Server Locations -->
      <div class="flex items-start gap-3">
        <span class="{isGolden ? 'text-amber-500' : 'text-primary'} text-lg">✓</span>
        <div>
          <span class="{typography.paragraph.base} font-medium">
            {isGolden ? 'Server များ-' : 'Server နေရာ-'}
          </span>
          <div class="flex flex-wrap gap-2 mt-2">
            <!-- Singapore Flag -->
            <div class="flex items-center gap-1 bg-gray-100 px-3 py-1 rounded-full">
              <span class="text-lg">🇸🇬</span>
              <span class="text-sm font-medium">Singapore</span>
            </div>

            {#if isGolden}
              <!-- Thailand Flag -->
              <div class="flex items-center gap-1 bg-gray-100 px-3 py-1 rounded-full">
                <span class="text-lg">🇹🇭</span>
                <span class="text-sm font-medium">Thailand</span>
              </div>

              <!-- Japan Flag -->
              <div class="flex items-center gap-1 bg-gray-100 px-3 py-1 rounded-full">
                <span class="text-lg">🇯🇵</span>
                <span class="text-sm font-medium">Japan</span>
              </div>

              <!-- Australia Flag -->
              <div class="flex items-center gap-1 bg-gray-100 px-3 py-1 rounded-full">
                <span class="text-lg">🇦🇺</span>
                <span class="text-sm font-medium">Australia</span>
              </div>
            {/if}
          </div>
        </div>
      </div>

      <!-- Anti-GFW Technology -->
      <div class="flex items-start gap-3">
        <span class="{isGolden ? 'text-amber-500' : 'text-primary'} text-lg">✓</span>
        <span class="{typography.paragraph.base} font-medium">
          {isGolden ? 'Advanced Anti-GFW Technology' : 'Standard Anti-GFW Protection'}
        </span>
      </div>

      <!-- Connection Speed -->
      <div class="flex items-start gap-3">
        <span class="{isGolden ? 'text-amber-500' : 'text-primary'} text-lg">✓</span>
        <span class="{typography.paragraph.base} font-medium">
          {isGolden ? 'လိုင်းဆွဲအားပိုမိုကောင်းမွန်' : 'လိုင်းဆွဲအားကောင်း'}
        </span>
      </div>
    </div>

    <a
      href="/plans/{product.id}"
      class="block w-full py-4 text-center {typography.special.button} text-lg {isGolden ? 'bg-gradient-to-r from-amber-500 to-amber-400' : 'bg-gradient-to-r from-primary to-primary/90'} text-white rounded-md hover:-translate-y-1 hover:shadow-lg transition-all duration-300 shadow-md relative overflow-hidden group"
    >
      <span class="relative z-10">ဝယ်မည်</span>
      <span class="absolute inset-0 {isGolden ? 'bg-gradient-to-r from-amber-600 to-amber-500' : 'bg-gradient-to-r from-primary-dark to-primary'} scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
    </a>
  </div>
</div>
