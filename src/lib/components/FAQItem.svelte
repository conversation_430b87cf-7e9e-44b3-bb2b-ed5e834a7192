<script lang="ts">
  import Typography from './Typography.svelte';
  import { typography } from '$lib/styles/typography';

  export let question: string;
  // We're using slots for the answer content, so no need for this prop
  // export let answer: string = '';
</script>

<div class="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 border border-accent/30 group">
  <div class="p-6 md:p-8">
    <!-- Question Section with improved spacing -->
    <div class="flex items-start gap-3">
      <div class="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center text-primary font-bold shrink-0 mt-1 group-hover:bg-primary group-hover:text-white transition-all duration-300 shadow-sm">
        <span>Q</span>
      </div>
      <div class="flex-1">
        <Typography variant="h3">{question}</Typography>
      </div>
    </div>

    <!-- Answer Section with improved styling -->
    <div class="mt-4"> <!-- Removed the indentation by aligning with question -->
      <div class="flex items-start gap-3">
        <div class="w-10 h-10 bg-accent/30 rounded-full flex items-center justify-center text-accent-dark font-bold shrink-0 mt-1 shadow-sm">
          <span>A</span>
        </div>
        <div class="flex-1">
          <div class="{typography.paragraph.base} text-lg">
            <slot />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
