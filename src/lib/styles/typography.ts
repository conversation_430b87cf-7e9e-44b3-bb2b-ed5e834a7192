// Typography utility for consistent text styling across the application

// Heading styles
export const headingStyles = {
  h1: 'text-5xl md:text-6xl font-heading font-bold text-secondary',
  h2: 'text-4xl md:text-5xl font-heading font-bold text-secondary',
  h3: 'text-3xl md:text-3xl font-heading font-bold text-secondary',
  h4: 'text-2xl md:text-2xl font-heading font-bold text-secondary',
  h5: 'text-2xl md:text-2xl font-heading font-semibold text-secondary',
  h6: 'text-2xl md:text-2xl font-heading font-semibold text-secondary',
};

// Paragraph styles
export const paragraphStyles = {
  lead: 'text-xl text-secondary/80 font-body',
  base: 'text-base text-secondary/80 font-body',
  small: 'text-sm text-secondary/70 font-body',
  tiny: 'text-xs text-secondary/60 font-body',
};

// Special text styles
export const specialTextStyles = {
  price: 'text-xl font-bold text-primary',
  priceLarge: 'text-2xl md:text-3xl font-bold text-primary',
  priceStrike: 'text-sm text-secondary/50 line-through',
  label: 'text-sm font-medium text-secondary',
  button: 'font-heading font-medium',
  link: 'text-primary hover:underline',
  error: 'text-sm text-red-500',
  success: 'text-sm text-green-500',
};

// Helper function to combine styles
export function combineStyles(...styles: string[]): string {
  return styles.join(' ');
}

// Export a combined object for easy imports
export const typography = {
  heading: headingStyles,
  paragraph: paragraphStyles,
  special: specialTextStyles,
  combine: combineStyles,
};

export default typography;
