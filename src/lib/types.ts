// Type definitions for the application

// Payment verification result
export interface PaymentVerificationResult {
  success: boolean;
  error?: string;
  status?:
    | 'verified'
    | 'invalid_receipt'
    | 'duplicate_transaction'
    | 'ai_error'
    | 'parse_error'
    | 'missing_image'
    | 'missing_order_id'
    | 'api_key_missing'
    | 'database_error'
    | 'ai_verification_bypassed'; // Added new status for AI bypass
  transactionId?: string;
  paymentService?: string;
  isDuplicate?: boolean;
  existingOrderId?: number;
  debug_error?: string;
  data?: any;
  message?: string; // Added for user messages, e.g., bypass info
  verificationId?: number; // Added to track the verification record
}

// Payment verification database record
export interface PaymentVerification {
  id?: number;
  transaction_id: string;
  order_id: number;
  payment_service?: string;
  verification_date?: string;
  image_path?: string;
  verification_status?: string;
  created_at?: string;
  updated_at?: string;
}
