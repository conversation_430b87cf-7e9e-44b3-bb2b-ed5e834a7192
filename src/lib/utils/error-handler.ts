import { json, type RequestEvent } from '@sveltejs/kit';
import { dev } from '$app/environment';

export class AppError extends Error {
  constructor(
    message: string,
    public code: string,
    public status: number = 500,
    public details?: any
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 'VALIDATION_ERROR', 400, details);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 'NOT_FOUND', 404);
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = 'Unauthorized') {
    super(message, 'UNAUTHORIZED', 401);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 'RATE_LIMIT_EXCEEDED', 429);
  }
}

export function handleApiError(error: unknown): Response {
  console.error('API Error:', error);

  if (error instanceof AppError) {
    return json(
      {
        success: false,
        error: error.message,
        code: error.code,
        ...(dev && error.details && { details: error.details })
      },
      { status: error.status }
    );
  }

  if (error instanceof Error) {
    // Don't expose internal error messages in production
    const message = dev ? error.message : 'Internal server error';
    return json(
      {
        success: false,
        error: message,
        code: 'INTERNAL_ERROR',
        ...(dev && { stack: error.stack })
      },
      { status: 500 }
    );
  }

  return json(
    {
      success: false,
      error: 'Unknown error occurred',
      code: 'UNKNOWN_ERROR'
    },
    { status: 500 }
  );
}

export function validateRequired(
  data: Record<string, any>,
  requiredFields: string[]
): void {
  const missing = requiredFields.filter(field => !data[field]);
  if (missing.length > 0) {
    throw new ValidationError(
      `Missing required fields: ${missing.join(', ')}`,
      { missing }
    );
  }
}

export function validateFileUpload(file: File, options: {
  maxSizeBytes: number;
  allowedTypes: readonly string[];
}): void {
  if (!file) {
    throw new ValidationError('No file provided');
  }

  if (file.size > options.maxSizeBytes) {
    throw new ValidationError(
      `File size exceeds limit of ${Math.round(options.maxSizeBytes / 1024 / 1024)}MB`
    );
  }

  if (!options.allowedTypes.includes(file.type)) {
    throw new ValidationError(
      `Invalid file type. Allowed types: ${options.allowedTypes.join(', ')}`
    );
  }
}

// Wrapper for API routes to handle errors consistently
export function withErrorHandling(
  handler: (event: RequestEvent) => Promise<Response>
) {
  return async (event: RequestEvent): Promise<Response> => {
    try {
      return await handler(event);
    } catch (error) {
      return handleApiError(error);
    }
  };
} 