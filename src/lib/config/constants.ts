// Product IDs
export const PRODUCTS = {
  BANDWIDTH_UPGRADE_ID: 1001,
} as const;

// File upload limits
export const FILE_UPLOAD = {
  MAX_SIZE_MB: 15,
  MAX_SIZE_BYTES: 15 * 1024 * 1024,
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
} as const;

// Payment details
export const PAYMENT = {
  RECEIVER_NAME: '<PERSON>hin <PERSON> Phyo',
  RECEIVER_PHONE: '09785968290',
  SERVICES: ['KBZ Pay', 'Wave Pay'],
  DEFAULT_PHONE: '09123456789', // Default phone for orders
} as const;

// Error messages in Myanmar
export const ERROR_MESSAGES = {
  INVALID_FILE_TYPE: 'ငွေလွှဲပြေစာ ss ပုံထည့်သွင်းပေးပါ။',
  FILE_TOO_LARGE: 'ပုံဆိုဒ် ကြီးနေသည်။ ဖုန်း Camera နှင့်ရိုက်ထားသောပုံဖြစ်ပါက ထိုပုံအား SS (စက်ခရင်ရှော့) ရိုက်၍ ပြန်ထည့်ပေးပါ။',
  INVALID_RECEIPT: 'ရှင်းလင်းပြတ်သား၍ မှန်ကန်သော ငွေလွှဲပြေစာ Screenshot ကိုထည့်ပေးပါ။',
  DUPLICATE_TRANSACTION: 'Order တင်ပြီးသား ငွေလွှဲပြေစာ Screenshot နှင့် နောက်တစ်ခါ Order ထပ်တင်၍မရနိုင်ပါ။ Order အသစ်တင်လိုပါက မှန်ကန်စွာငွေလွှဲပြီး ပြေစာအသစ်ထည့်ပေးပါ။',
  GENERIC_ERROR: 'Error တက်သွားသည်။ ထပ်မံကြိုးစားပါ။',
  NAME_REQUIRED: 'နာမည်ထည့်ပေးပါ။',
  PHONE_REQUIRED: 'ဖုန်းနံပတ်ထည့်ပေးပါ။',
  PAYMENT_PROOF_REQUIRED: 'ငွေလွှဲပြီး ပြေစာ SS ထည့်ပေးပါ။',
  PROCESSING: 'ခဏစောင့်ပါ...',
} as const;

// API endpoints
export const API_ENDPOINTS = {
  UPLOAD_PAYMENT_PROOF: '/api/upload-payment-proof',
  CREATE_ORDER: '/api/create-order',
  PAYMENT_VERIFICATION: '/api/payment-verification/verify',
  UPDATE_ORDER_ID: '/api/payment-verification/update-order-id',
} as const;

// Phone number validation regex for Myanmar
export const PHONE_REGEX = /^09\d{7,11}$/;

// Meta Pixel events
export const TRACKING_CONFIG = {
  CURRENCY: 'MMK',
  CONTENT_CATEGORIES: {
    HOME: 'Homepage',
    PRODUCT: 'Product',
    VPN_SERVICES: 'VPN Services',
  },
} as const; 