@import 'tailwindcss';

@theme {
  --color-primary: #7338f2; /* Purple from logo */
  --color-secondary: #271f30; /* Dark purple/navy from logo */
  --color-accent: #e4e2ff; /* Light lavender from logo */
}

@font-face {
  font-family: 'Z17Strength';
  src: url('/fonts/Z17Strength-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Z17Strength';
  src: url('/fonts/Z17Strength-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Z11-MyanSans';
  src: url('/fonts/Z11-MyanSans-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Z11-MyanSans';
  src: url('/fonts/Z11-MyanSans-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Z11-MyanSans';
  src: url('/fonts/Z11-MyanSans-Thin.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

body {
  font-family: 'Z11-MyanSans', sans-serif;
  color: var(--color-secondary);
  background-color: var(--color-accent);
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Z17Strength', sans-serif;
}
