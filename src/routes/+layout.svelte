<script lang="ts">
	import '../app.css';
	import Header from '$lib/components/Header.svelte';
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { initMetaPixel, trackPageView, initTrackingIds, getExternalId } from '$lib/services/meta-pixel';
	import { page } from '$app/stores';

	let { children } = $props();

	// Initialize tracking IDs as early as possible
	if (browser) {
		initTrackingIds();
	}

	// Flag to track if initial PageView has been sent
	let initialPageViewSent = false;

	// Initialize Meta Pixel on mount
	onMount(() => {
		// Initialize Meta Pixel with enhanced PageView tracking
		initMetaPixel();

		// The initMetaPixel function now automatically tracks the initial PageView
		// with all necessary parameters including fbp and external_id
		initialPageViewSent = true;
	});

	// Track PageView on route changes
	const currentPath = $derived($page.url.pathname);

	// Previous path to detect actual route changes
	let previousPath = '';

	$effect(() => {
		// Only track PageView for actual route changes, not the initial page load
		if (browser && currentPath && initialPageViewSent && currentPath !== previousPath) {
			console.log('Route changed, tracking PageView for:', currentPath);

			// Use the dedicated trackPageView function which ensures all parameters are included
			trackPageView({
				page_path: currentPath,
				page_title: document.title,
				page_location: window.location.href,
				external_id: getExternalId(), // Use the persistent external ID
				route_change: true // Add a flag to indicate this is from a route change
			});

			// Update previous path
			previousPath = currentPath;
		} else if (currentPath && !previousPath) {
			// Initialize previousPath on first run
			previousPath = currentPath;
		}
	})
</script>

<div class="flex flex-col min-h-screen bg-accent">
	<Header />

	<main class="flex-grow pt-24">
		{@render children()}
	</main>

	<footer class="bg-secondary text-accent py-4 text-center text-sm">
		<div class="container mx-auto">
			&copy; {new Date().getFullYear()} IPman VPN. All rights reserved.
		</div>
	</footer>
</div>
