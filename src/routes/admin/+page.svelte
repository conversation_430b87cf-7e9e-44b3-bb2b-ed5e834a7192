<script lang="ts">
  import { onMount } from 'svelte';

  interface PaymentVerification {
    id: number;
    transaction_id: string;
    order_id: number;
    payment_service?: string;
    verification_date: string;
    image_path?: string;
    verification_status: string;
    created_at: string;
    updated_at: string;
  }

  let stats = {
    totalVerifications: 0,
    todayVerifications: 0,
    recentVerifications: [] as PaymentVerification[]
  };
  let loading = true;

  onMount(async () => {
    await loadStats();
  });

  async function loadStats() {
    try {
      const response = await fetch('/api/admin/payment-verifications?limit=5');
      const result = await response.json();
      
      if (result.success) {
        stats.totalVerifications = result.pagination.total;
        stats.recentVerifications = result.data;
        
        // Count today's verifications
        const today = new Date().toDateString();
        stats.todayVerifications = result.data.filter((v: PaymentVerification) =>
          new Date(v.created_at).toDateString() === today
        ).length;
      }
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      loading = false;
    }
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleString();
  }
</script>

<svelte:head>
  <title>Admin Dashboard - IPMan VPN</title>
</svelte:head>

<div class="space-y-6">
  <div>
    <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
    <p class="text-gray-600 mt-2">Manage your IPMan VPN payment verifications</p>
  </div>

  {#if loading}
    <div class="text-center py-8">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <p class="mt-2 text-gray-600">Loading dashboard...</p>
    </div>
  {:else}
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="bg-white p-6 rounded-lg shadow-md">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 text-blue-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Verifications</p>
            <p class="text-2xl font-bold text-gray-900">{stats.totalVerifications}</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-md">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 text-green-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Today's Verifications</p>
            <p class="text-2xl font-bold text-gray-900">{stats.todayVerifications}</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-md">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-purple-100 text-purple-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">System Status</p>
            <p class="text-2xl font-bold text-green-600">Online</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white p-6 rounded-lg shadow-md">
      <h2 class="text-xl font-bold text-gray-900 mb-4">Quick Actions</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <a 
          href="/admin/payment-verifications" 
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <div class="p-2 bg-blue-100 text-blue-600 rounded-lg mr-4">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <div>
            <h3 class="font-medium text-gray-900">View All Verifications</h3>
            <p class="text-sm text-gray-600">Manage payment verification records</p>
          </div>
        </a>

        <div class="flex items-center p-4 border border-gray-200 rounded-lg bg-gray-50">
          <div class="p-2 bg-gray-100 text-gray-400 rounded-lg mr-4">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z"></path>
            </svg>
          </div>
          <div>
            <h3 class="font-medium text-gray-400">Analytics (Coming Soon)</h3>
            <p class="text-sm text-gray-400">View detailed analytics and reports</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Verifications -->
    <div class="bg-white p-6 rounded-lg shadow-md">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold text-gray-900">Recent Verifications</h2>
        <a 
          href="/admin/payment-verifications" 
          class="text-blue-600 hover:text-blue-800 text-sm font-medium"
        >
          View All →
        </a>
      </div>
      
      {#if stats.recentVerifications.length > 0}
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Service</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {#each stats.recentVerifications as verification}
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                    {verification.transaction_id}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {verification.order_id}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {verification.payment_service || 'Unknown'}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatDate(verification.created_at)}
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      {:else}
        <p class="text-gray-600 text-center py-4">No recent verifications found.</p>
      {/if}
    </div>
  {/if}
</div>
