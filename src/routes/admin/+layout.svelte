<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { browser } from '$app/environment';

  let isAuthenticated = false;
  let password = '';
  let showLogin = true;
  let loginError = '';
  let debugInfo = '';

  onMount(() => {
    // Check if already authenticated with session token
    const authToken = localStorage.getItem('admin_auth_token');
    const authExpiry = localStorage.getItem('admin_auth_expiry');

    if (authToken && authExpiry && Date.now() < parseInt(authExpiry)) {
      isAuthenticated = true;
      showLogin = false;
    } else {
      // Clear expired tokens
      localStorage.removeItem('admin_auth_token');
      localStorage.removeItem('admin_auth_expiry');
    }
  });

  async function handleLogin() {
    debugInfo = 'Attempting login...';

    try {
      // Try the secure API authentication first
      debugInfo = 'Trying secure API auth...';
      const response = await fetch('/api/admin/auth', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password })
      });

      debugInfo = `API response status: ${response.status}`;

      if (response.ok) {
        const result = await response.json();
        debugInfo = `API result: ${JSON.stringify(result)}`;

        if (result.success) {
          isAuthenticated = true;
          showLogin = false;
          loginError = '';
          debugInfo = 'Secure auth successful!';

          if (browser) {
            // Store secure session token with expiry (24 hours)
            const expiryTime = Date.now() + (24 * 60 * 60 * 1000);
            localStorage.setItem('admin_auth_token', result.token);
            localStorage.setItem('admin_auth_expiry', expiryTime.toString());
          }
          return;
        } else {
          loginError = result.error || 'Invalid password';
          password = '';
          debugInfo = `API auth failed: ${result.error}`;
          return;
        }
      }
    } catch (error) {
      debugInfo = `API error: ${(error as Error).message}`;
      console.log('Authentication failed:', error);
      loginError = 'Authentication failed. Please check your password and try again.';
      password = '';
    }
  }

  function handleLogout() {
    isAuthenticated = false;
    showLogin = true;
    if (browser) {
      localStorage.removeItem('admin_auth_token');
      localStorage.removeItem('admin_auth_expiry');
    }
    goto('/');
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      handleLogin();
    }
  }
</script>

<svelte:head>
  <title>Admin Panel - IPMan VPN</title>
</svelte:head>

{#if showLogin && !isAuthenticated}
  <div class="min-h-screen bg-gray-100 flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
      <h1 class="text-2xl font-bold text-center mb-6">Admin Login</h1>
      
      {#if loginError}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {loginError}
        </div>
      {/if}

      {#if debugInfo}
        <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4 text-sm">
          Debug: {debugInfo}
        </div>
      {/if}
      
      <div class="mb-4">
        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
          Password
        </label>
        <input
          type="password"
          id="password"
          bind:value={password}
          on:keydown={handleKeydown}
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter admin password"
        />
      </div>
      
      <button
        on:click={handleLogin}
        class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
      >
        Login
      </button>
      
      <div class="mt-4 text-center">
        <a href="/" class="text-blue-600 hover:text-blue-800 text-sm">
          ← Back to Home
        </a>
      </div>
    </div>
  </div>
{:else if isAuthenticated}
  <div class="min-h-screen bg-gray-100">
    <!-- Admin Header -->
    <header class="bg-white shadow-sm border-b">
      <div class="container mx-auto px-4 py-4">
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-4">
            <h1 class="text-xl font-bold text-gray-900">IPMan VPN Admin</h1>
            <nav class="flex space-x-4">
              <a 
                href="/admin/payment-verifications" 
                class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium {$page.url.pathname.includes('/payment-verifications') ? 'bg-gray-100' : ''}"
              >
                Payment Verifications
              </a>
            </nav>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600">Admin Panel</span>
            <button
              on:click={handleLogout}
              class="text-red-600 hover:text-red-800 text-sm font-medium"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
      <slot />
    </main>
  </div>
{/if}

<style>
  :global(body) {
    margin: 0;
    padding: 0;
  }
</style>
