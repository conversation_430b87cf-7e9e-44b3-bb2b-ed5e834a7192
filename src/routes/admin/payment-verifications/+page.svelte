<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  
  interface PaymentVerification {
    id: number;
    transaction_id: string;
    order_id: number;
    payment_service?: string;
    verification_date: string;
    image_path?: string;
    verification_status: string;
    created_at: string;
    updated_at: string;
  }

  interface PaginationInfo {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  }

  let verifications: PaymentVerification[] = [];
  let pagination: PaginationInfo = { page: 1, limit: 20, total: 0, totalPages: 0 };
  let loading = false;
  let searchQuery = '';
  let selectedImage = '';
  let showImageModal = false;

  // Get current page from URL
  $: currentPage = parseInt($page.url.searchParams.get('page') || '1');
  $: searchParam = $page.url.searchParams.get('search') || '';

  onMount(() => {
    searchQuery = searchParam;
    loadVerifications();
  });

  async function loadVerifications() {
    loading = true;
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20'
      });

      if (searchQuery.trim()) {
        params.set('search', searchQuery.trim());
      }

      // Get auth token for API request
      const authToken = localStorage.getItem('admin_auth_token');
      const headers: HeadersInit = {};
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }

      const response = await fetch(`/api/admin/payment-verifications?${params}`, {
        headers
      });
      const result = await response.json();
      
      if (result.success) {
        verifications = result.data;
        pagination = result.pagination;
      } else {
        console.error('Failed to load verifications:', result.error);
      }
    } catch (error) {
      console.error('Error loading verifications:', error);
    } finally {
      loading = false;
    }
  }

  async function deleteVerification(id: number) {
    if (!confirm('Are you sure you want to delete this payment verification?')) {
      return;
    }

    try {
      // Get auth token for API request
      const authToken = localStorage.getItem('admin_auth_token');
      const headers: HeadersInit = {};
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }

      const response = await fetch(`/api/admin/payment-verifications?id=${id}`, {
        method: 'DELETE',
        headers
      });

      const result = await response.json();
      
      if (result.success) {
        await loadVerifications(); // Reload the list
      } else {
        alert('Failed to delete verification: ' + result.error);
      }
    } catch (error) {
      console.error('Error deleting verification:', error);
      alert('Error deleting verification');
    }
  }

  function handleSearch() {
    const params = new URLSearchParams($page.url.searchParams);
    if (searchQuery.trim()) {
      params.set('search', searchQuery.trim());
    } else {
      params.delete('search');
    }
    params.set('page', '1'); // Reset to first page
    goto(`?${params.toString()}`);
  }

  function goToPage(pageNum: number) {
    const params = new URLSearchParams($page.url.searchParams);
    params.set('page', pageNum.toString());
    goto(`?${params.toString()}`);
  }

  function showImage(imagePath: string | undefined) {
    if (imagePath) {
      selectedImage = `/uploads/payment-proofs/${imagePath}`;
      showImageModal = true;
    }
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleString();
  }

  // Reactive statement to reload when URL changes
  $: if (currentPage || searchParam !== searchQuery) {
    searchQuery = searchParam;
    loadVerifications();
  }
</script>

<svelte:head>
  <title>Payment Verifications Admin - IPMan VPN</title>
</svelte:head>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-900">Payment Verifications</h1>
    <div class="text-sm text-gray-600">
      Total: {pagination.total} records
    </div>
  </div>

  <!-- Search -->
  <div class="mb-6">
    <div class="flex gap-2">
      <input
        type="text"
        bind:value={searchQuery}
        placeholder="Search by transaction ID, order ID, or payment service..."
        class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        on:keydown={(e) => e.key === 'Enter' && handleSearch()}
      />
      <button
        on:click={handleSearch}
        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        Search
      </button>
    </div>
  </div>

  <!-- Loading -->
  {#if loading}
    <div class="text-center py-8">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <p class="mt-2 text-gray-600">Loading...</p>
    </div>
  {:else}
    <!-- Table -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction ID</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Service</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            {#each verifications as verification (verification.id)}
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {verification.id}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                  {verification.transaction_id}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {verification.order_id}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {verification.payment_service || 'Unknown'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    {verification.verification_status}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatDate(verification.created_at)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {#if verification.image_path}
                    <button
                      on:click={() => showImage(verification.image_path)}
                      class="text-blue-600 hover:text-blue-800 underline"
                    >
                      View Image
                    </button>
                  {:else}
                    <span class="text-gray-400">No image</span>
                  {/if}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    on:click={() => deleteVerification(verification.id)}
                    class="text-red-600 hover:text-red-900 transition-colors"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Pagination -->
    {#if pagination.totalPages > 1}
      <div class="mt-6 flex justify-center">
        <nav class="flex space-x-2">
          <button
            on:click={() => goToPage(currentPage - 1)}
            disabled={currentPage <= 1}
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          {#each Array.from({length: Math.min(5, pagination.totalPages)}, (_, i) => {
            const start = Math.max(1, currentPage - 2);
            return start + i;
          }) as pageNum}
            {#if pageNum <= pagination.totalPages}
              <button
                on:click={() => goToPage(pageNum)}
                class="px-3 py-2 text-sm font-medium {pageNum === currentPage ? 'text-blue-600 bg-blue-50 border-blue-500' : 'text-gray-500 bg-white border-gray-300'} border rounded-md hover:bg-gray-50"
              >
                {pageNum}
              </button>
            {/if}
          {/each}
          
          <button
            on:click={() => goToPage(currentPage + 1)}
            disabled={currentPage >= pagination.totalPages}
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </nav>
      </div>
    {/if}
  {/if}
</div>

<!-- Image Modal -->
{#if showImageModal}
  <div
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    role="dialog"
    aria-modal="true"
    aria-labelledby="modal-title"
    tabindex="-1"
    on:click={() => showImageModal = false}
    on:keydown={(e) => e.key === 'Escape' && (showImageModal = false)}
  >
    <div
      class="bg-white p-4 rounded-lg max-w-4xl max-h-[90vh] overflow-auto"
      role="document"
    >
      <div class="flex justify-between items-center mb-4">
        <h3 id="modal-title" class="text-lg font-semibold">Payment Proof Image</h3>
        <button
          on:click={() => showImageModal = false}
          class="text-gray-500 hover:text-gray-700 text-2xl"
          aria-label="Close modal"
        >
          ×
        </button>
      </div>
      <img src={selectedImage} alt="Payment proof" class="max-w-full h-auto" />
    </div>
  </div>
{/if}
