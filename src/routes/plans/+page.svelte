<script lang="ts">
  import { goto } from '$app/navigation';
  import type { Product } from '$lib/services/woocommerce';
  import { onMount } from 'svelte';
  import { trackEvent, MetaPixelEvent } from '$lib/services/meta-pixel';
  import PlanCard from '$lib/components/PlanCard.svelte';

  // Define the expected data structure
  interface PageData {
    products: Product[];
    apiError?: string;
    error?: string;
  }

  // Get data from server load function
  let { data } = $props<{ data: PageData }>();

  // Ensure data is properly initialized
  const safeData = {
    products: Array.isArray(data?.products) ? data.products : [],
    apiError: data?.apiError,
    error: data?.error
  };

  // State for products
  let products: Product[] = $state(safeData.products);
  // Get apiConfigured status from server load function
  let apiConfigured = $state(data?.apiConfigured ?? false); 
  let apiError = $state(safeData.apiError);
  let error = $state(safeData.error);

  // Track ViewCategory event when the component mounts
  onMount(() => {
    if (products.length > 0) {
      trackEvent(MetaPixelEvent.VIEW_CONTENT, {
        content_type: 'product_group',
        content_ids: products.map(p => p.id.toString()),
        content_name: 'VPN Plans',
        content_category: 'VPN Plans',
        currency: 'MMK'
      });
    }
  });

  // Format price with currency symbol
  function formatPrice(price: string): string {
    if (!price) return '';
    return `${price} Ks`;
  }
</script>

<div class="container mx-auto px-4 py-16">
  <h1 class="text-4xl md:text-5xl font-heading font-bold text-secondary mb-8 text-center">
    IPမန်း VPN Plans
  </h1>
  <p class="text-lg mb-12 text-secondary/80 max-w-3xl mx-auto text-center">
    ကြိုက်နှစ်သက်ရာ VPN Plan အားရွေးချယ်ပါ။
  </p>

  {#if !apiConfigured}
    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-8" role="alert">
      <strong class="font-bold">API Not Configured!</strong>
      <span class="block sm:inline">Please set up your WooCommerce API credentials in the .env file.</span>
    </div>
  {/if}

  {#if apiError}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-8" role="alert">
      <strong class="font-bold">API Error!</strong>
      <span class="block sm:inline">{apiError}</span>
    </div>
  {/if}

  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-8" role="alert">
      <strong class="font-bold">Error!</strong>
      <span class="block sm:inline">{error}</span>
    </div>
  {/if}

  {#if products.length === 0}
    <div class="text-center py-12">
      <p class="text-xl text-secondary/70">No plans found.</p>
    </div>
  {:else}
    <div class="flex flex-col md:flex-row justify-center items-stretch gap-8 max-w-5xl mx-auto">
      {#each products as product, index}
        {#if product.id !== 1001}
          <div class="w-full md:w-1/2 max-w-md mx-auto flex">
            <div class="w-full flex-1 flex">
              <PlanCard
                product={product}
                isGolden={product.name.toLowerCase().includes('golden') || index === 1}
                className="flex-1 flex flex-col h-full"
              />
            </div>
          </div>
        {/if}
      {/each}
    </div>
  {/if}
</div>
