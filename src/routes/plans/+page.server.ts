import type { PageServerLoad } from './$types';
import { wooCommerceService } from '$lib/server/woocommerce.server'; // Updated import path
import { env } from '$env/dynamic/private';

export const load: PageServerLoad = async () => {
  try {
    // Check if WooCommerce API is configured using private env vars
    const apiConfigured = !!env.WOOCOMMERCE_URL && 
                         !!env.WOOCOMMERCE_CONSUMER_KEY && 
                         !!env.WOOCOMMERCE_CONSUMER_SECRET;
    
    if (!apiConfigured) {
      console.warn('WooCommerce API not configured. Please set up your API credentials in .env');
      return {
        products: [],
        apiConfigured: false
      };
    }
    
    try {
      // Fetch products
      const products = await wooCommerceService.getProducts();
      
      return {
        products,
        apiConfigured: true
      };
    } catch (apiErr) {
      console.error('WooCommerce API error:', apiErr);
      return {
        products: [],
        apiError: apiErr instanceof Error ? apiErr.message : 'Unknown API error'
      };
    }
  } catch (err) {
    console.error('Error in load function:', err);
    // Return empty data instead of throwing an error
    return {
      products: [],
      error: err instanceof Error ? err.message : 'Unknown error'
    };
  }
};
