<script lang="ts">
  import type { Product, ProductVariation, ProductAttribute, VariationAttribute } from '$lib/services/woocommerce';
  import CheckoutForm from '$lib/components/CheckoutForm.svelte';
  import OrderConfirmation from '$lib/components/OrderConfirmation.svelte';
  import { onMount } from 'svelte';
  import { trackEvent, MetaPixelEvent } from '$lib/services/meta-pixel';
  import { getBooleanUrlParam, getNumericUrlParam, getUrlParam, setUrlParams } from '$lib/utils/url';

  // Define the expected data structure
  interface PageData {
    product: Product | null;
    variations?: ProductVariation[];
    apiConfigured?: boolean;
    notFound?: boolean;
    apiError?: string;
    error?: string;
  }

  // Get data from server load function
  let { data } = $props<{ data: PageData }>();

  // Track ViewContent event when the component mounts
  onMount(() => {
    if (product) {
      trackEvent(MetaPixelEvent.VIEW_CONTENT, {
        content_type: 'product',
        content_ids: [product.id.toString()],
        content_name: product.name,
        content_category: product.categories && Array.isArray(product.categories) ? product.categories.map((cat: any) => cat.name).join(', ') : '',
        value: product.price ? parseFloat(product.price) : 0,
        currency: 'MMK'
      });
    }
  });

  // State for product and error states
  let product = $state(data.product);
  let variations = $state(data.variations || []);
  let apiConfigured = $state(data.apiConfigured !== false); // Default to true if not explicitly false
  let notFound = $state(data.notFound === true);
  let apiError = $state(data.apiError);
  let error = $state(data.error);

  // Local storage keys
  const STORAGE_KEY_PREFIX = `ipman_product_${data.product?.id || 'unknown'}_`;
  const STORAGE_KEYS = {
    selectedAttributes: `${STORAGE_KEY_PREFIX}selectedAttributes`,
    selectedVariation: `${STORAGE_KEY_PREFIX}selectedVariation`,
    showCheckout: `${STORAGE_KEY_PREFIX}showCheckout`,
    showOrderConfirmation: `${STORAGE_KEY_PREFIX}showOrderConfirmation`,
    orderId: `${STORAGE_KEY_PREFIX}orderId`,
    orderNumber: `${STORAGE_KEY_PREFIX}orderNumber`,
  };

  // State for selected variation
  let selectedVariation = $state<ProductVariation | null>(null);
  let selectedAttributes = $state<Record<string, string>>({});

  // Checkout state
  let showCheckout = $state(false);
  let showOrderConfirmation = $state(false);
  let orderId = $state<number | null>(null);
  let orderNumber = $state<string>('');

  // Load state from URL parameters on component mount
  onMount(() => {
    if (typeof window !== 'undefined') {
      try {
        // Check for modal state in URL parameters
        const showCheckoutParam = getBooleanUrlParam('checkout');
        const showOrderConfirmationParam = getBooleanUrlParam('order_confirmation');
        const orderIdParam = getNumericUrlParam('order_id');
        const orderNumberParam = getUrlParam('order_number');

        // Set modal states based on URL parameters
        showCheckout = showCheckoutParam === true;
        showOrderConfirmation = showOrderConfirmationParam === true;

        if (orderIdParam !== null) {
          orderId = orderIdParam;
        }

        if (orderNumberParam !== null) {
          orderNumber = orderNumberParam;
        }

        // For selected attributes and variations, we could also use URL parameters
        // but for now we'll keep using localStorage for these since they're more complex
        try {
          // Load selected attributes
          const savedAttributes = localStorage.getItem(STORAGE_KEYS.selectedAttributes);
          if (savedAttributes) {
            selectedAttributes = JSON.parse(savedAttributes);
          }

          // Load selected variation
          const savedVariation = localStorage.getItem(STORAGE_KEYS.selectedVariation);
          if (savedVariation) {
            const parsedVariation = JSON.parse(savedVariation);
            // Find the matching variation from our loaded variations
            selectedVariation = variations.find((v: ProductVariation) => v.id === parsedVariation.id) || null;
          }
        } catch (error) {
          console.error('Error loading attributes/variations from localStorage:', error);
        }
      } catch (error) {
        console.error('Error loading state from URL parameters:', error);
      }
    }
  });

  // Function to update URL parameters and save some state to localStorage
  function updateState() {
    if (typeof window !== 'undefined') {
      try {
        // Update URL parameters for modal states
        setUrlParams({
          'checkout': showCheckout ? 'true' : null,
          'order_confirmation': showOrderConfirmation ? 'true' : null,
          'order_id': orderId ? orderId.toString() : null,
          'order_number': orderNumber || null
        });

        // Still save selected attributes and variation to localStorage
        // as they're more complex and not directly related to modal state
        localStorage.setItem(STORAGE_KEYS.selectedAttributes, JSON.stringify(selectedAttributes));

        if (selectedVariation) {
          localStorage.setItem(STORAGE_KEYS.selectedVariation, JSON.stringify(selectedVariation));
        } else {
          localStorage.removeItem(STORAGE_KEYS.selectedVariation);
        }
      } catch (error) {
        console.error('Error updating state:', error);
      }
    }
  }

  // Function to handle attribute selection
  function handleAttributeChange(attributeName: string, optionValue: string) {
    selectedAttributes[attributeName] = optionValue;
    console.log('Selected attributes:', selectedAttributes);

    // Find matching variation based on selected attributes
    if (variations.length > 0) {
      // Only consider a variation a match if ALL selected attributes match
      const matchingVariation = variations.find((variation: ProductVariation) => {
        // Check if all selected attributes match this variation
        return Object.entries(selectedAttributes).every(([attrName, attrValue]) => {
          // Case-insensitive comparison for attribute names
          const matchingAttribute = variation.attributes.find((attr: VariationAttribute) =>
            attr.name.toLowerCase() === attrName.toLowerCase());

          console.log('Checking variation attribute:', matchingAttribute, 'against selected value:', attrValue);
          return matchingAttribute && matchingAttribute.option === attrValue;
        });
      });

      console.log('Matching variation:', matchingVariation);
      selectedVariation = matchingVariation || null;
    }

    // Update state
    updateState();
  }

  // Get unique attributes from variations
  function getUniqueAttributes(): ProductAttribute[] {
    if (!product || !product.attributes) return [];

    // Filter only variation attributes
    return product.attributes.filter((attr: ProductAttribute) => attr.variation);
  }

  // Get available options for an attribute
  function getAttributeOptions(attributeName: string): string[] {
    const attribute = product?.attributes.find((attr: ProductAttribute) => attr.name === attributeName);
    return attribute?.options || [];
  }

  // Format price with currency symbol
  function formatPrice(price: string): string {
    if (!price) return '';
    return `${price} Ks`;
  }

  // Calculate discount percentage
  function calculateDiscount(regularPrice: string, salePrice: string): number {
    if (!regularPrice || !salePrice) return 0;

    const regular = parseFloat(regularPrice);
    const sale = parseFloat(salePrice);

    if (regular <= 0 || sale <= 0 || sale >= regular) return 0;

    return Math.round(((regular - sale) / regular) * 100);
  }

  // Handle buy now button click
  function handleBuyNow() {
    // For variable products, make sure a variation is selected
    if (product?.type === 'variable' && !selectedVariation) {
      // Scroll to the variation selection
      const variationSection = document.getElementById('variation-section');
      if (variationSection) {
        variationSection.scrollIntoView({ behavior: 'smooth' });
      }
      return;
    }

    // Track InitiateCheckout event
    const price = selectedVariation
      ? (selectedVariation.on_sale ? parseFloat(selectedVariation.sale_price) : parseFloat(selectedVariation.regular_price))
      : (product?.on_sale ? parseFloat(product.sale_price) : parseFloat(product.regular_price));

    trackEvent(MetaPixelEvent.INITIATE_CHECKOUT, {
      content_type: 'product',
      content_ids: [product?.id.toString()],
      content_name: product?.name,
      value: price || 0,
      currency: 'MMK',
      contents: [
        {
          id: selectedVariation ? selectedVariation.id.toString() : product?.id.toString(),
          quantity: 1,
          item_price: price || 0
        }
      ]
    });

    showCheckout = true;
    updateState();
  }

  // Handle checkout success
  function handleCheckoutSuccess(event: { orderId: number; orderNumber: string }) {
    orderId = event.orderId;
    orderNumber = event.orderNumber;
    showCheckout = false;
    showOrderConfirmation = true;
    updateState();
  }

  // Handle checkout cancel
  function handleCheckoutCancel() {
    showCheckout = false;
    updateState();
  }

  // Handle order confirmation close
  function handleConfirmationClose() {
    showOrderConfirmation = false;
    orderId = null;
    orderNumber = '';
    updateState();
  }
</script>

<div class="container mx-auto px-4 py-16">
  <div class="mb-8">
    <a href="/plans" class="text-primary hover:underline flex items-center">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
      </svg>
      Back to Plans
    </a>
  </div>

  {#if !apiConfigured}
    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-8" role="alert">
      <strong class="font-bold">API Not Configured!</strong>
      <span class="block sm:inline">Please set up your WooCommerce API credentials in the .env file.</span>
      <p class="mt-2">
        See the <a href="/WOOCOMMERCE_SETUP.md" class="underline">setup guide</a> for instructions.
      </p>
    </div>
  {:else if apiError}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-8" role="alert">
      <strong class="font-bold">API Error!</strong>
      <span class="block sm:inline">{apiError}</span>
    </div>
  {:else if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-8" role="alert">
      <strong class="font-bold">Error!</strong>
      <span class="block sm:inline">{error}</span>
    </div>
  {:else if notFound}
    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-8" role="alert">
      <strong class="font-bold">Product Not Found!</strong>
      <span class="block sm:inline">The requested product could not be found.</span>
    </div>
  {:else if product}
    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
      <div class="md:flex">
        <!-- Product Image -->
        <div class="md:w-1/2 relative">
          {#if product.images && product.images.length > 0}
            {@const image = product.images[0]}
            {@const originalSrc = image.src}
            <picture>
              <source
                srcset={`/api/image-optimizer?url=${encodeURIComponent(originalSrc)}&w=400&format=avif 400w, /api/image-optimizer?url=${encodeURIComponent(originalSrc)}&w=800&format=avif 800w`}
                type="image/avif"
              />
              <source
                srcset={`/api/image-optimizer?url=${encodeURIComponent(originalSrc)}&w=400&format=webp 400w, /api/image-optimizer?url=${encodeURIComponent(originalSrc)}&w=800&format=webp 800w`}
                type="image/webp"
              />
              <source
                srcset={`/api/image-optimizer?url=${encodeURIComponent(originalSrc)}&w=400&format=jpeg 400w, /api/image-optimizer?url=${encodeURIComponent(originalSrc)}&w=800&format=jpeg 800w`}
                type="image/jpeg"
              />
              <img
                src={`/api/image-optimizer?url=${encodeURIComponent(originalSrc)}&w=800&format=jpeg`}
                alt={image.alt || product.name}
                class="w-full h-full object-cover"
                loading="lazy"
                width="800" 
              />
            </picture>
            {#if product.on_sale}
              <div class="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full font-bold text-sm">
                SALE
              </div>
            {/if}
          {:else}
            <div class="bg-accent w-full h-full flex items-center justify-center p-12">
              <span class="text-secondary/50 text-xl">No image available</span>
            </div>
          {/if}
        </div>

        <!-- Product Details -->
        <div class="md:w-1/2 p-8">
          <h1 class="text-3xl md:text-4xl font-heading font-bold text-secondary mb-4">
            {product.name}
          </h1>

          <!-- Price -->
          <div class="mb-6">
            {#if product.type === 'variable'}
              {#if selectedVariation}
                <!-- Selected variation price -->
                {#if selectedVariation.on_sale}
                  <div class="flex items-center">
                    <span class="text-3xl font-bold text-primary mr-3">{formatPrice(selectedVariation.sale_price)}</span>
                    <span class="text-xl text-secondary/50 line-through">{formatPrice(selectedVariation.regular_price)}</span>
                    <span class="ml-3 bg-red-500 text-white px-2 py-1 rounded-full text-sm font-bold">
                      {calculateDiscount(selectedVariation.regular_price, selectedVariation.sale_price)}% OFF
                    </span>
                  </div>
                {:else}
                  <span class="text-3xl font-bold text-primary">{formatPrice(selectedVariation.regular_price)}</span>
                {/if}
              {:else}
                <!-- Variable product price range -->
                <span class="text-3xl font-bold text-primary">{@html product.price_html}</span>
              {/if}
            {:else}
              <!-- Simple product price -->
              {#if product.on_sale}
                <div class="flex items-center">
                  <span class="text-3xl font-bold text-primary mr-3">{formatPrice(product.sale_price)}</span>
                  <span class="text-xl text-secondary/50 line-through">{formatPrice(product.regular_price)}</span>
                  <span class="ml-3 bg-red-500 text-white px-2 py-1 rounded-full text-sm font-bold">
                    {calculateDiscount(product.regular_price, product.sale_price)}% OFF
                  </span>
                </div>
              {:else}
                <span class="text-3xl font-bold text-primary">{formatPrice(product.regular_price)}</span>
              {/if}
            {/if}
          </div>

          <!-- Description -->
          <div class="mb-8 prose prose-lg">
            {product.description || product.short_description || 'No description available'}
          </div>

          <!-- Variation Attributes (for variable products) -->
          {#if product.type === 'variable' && variations.length > 0}
            <div class="mb-8" id="variation-section">
              <h3 class="text-lg font-bold text-secondary mb-4">Options:</h3>

              {#each getUniqueAttributes() as attribute, i}
                <div class="mb-4">
                  <div class="block text-secondary font-medium mb-2" id="attr-label-{i}">{attribute.name}:</div>
                  <div class="flex flex-wrap gap-2" role="radiogroup" aria-labelledby="attr-label-{i}">
                    {#each getAttributeOptions(attribute.name) as option}
                      <button
                        class="px-4 py-2 border rounded-md transition-all duration-300 {selectedAttributes[attribute.name] === option ? 'bg-primary text-white border-primary' : 'bg-white text-secondary border-accent hover:border-primary'}"
                        onclick={() => handleAttributeChange(attribute.name, option)}
                        role="radio"
                        aria-checked={selectedAttributes[attribute.name] === option}
                      >
                        {option}
                      </button>
                    {/each}
                  </div>
                </div>
              {/each}
            </div>
          {/if}

          <!-- Buy Now Button -->
          <button
            class="w-full py-3 bg-primary text-white font-heading rounded-md hover:bg-opacity-90 transition-all duration-300 shadow-md {(product.type === 'variable' && !selectedVariation) ? 'opacity-50 cursor-not-allowed' : ''}"
            disabled={product.type === 'variable' && !selectedVariation}
            onclick={handleBuyNow}
          >
            {product.type === 'variable' && !selectedVariation ? 'GB ပမာဏရွေးချယ်ပါ' : 'ဝယ်မည်'}
          </button>

          {#if product.type === 'variable' && !selectedVariation && Object.keys(selectedAttributes).length > 0}
            <p class="text-red-500 text-sm mt-2">Please select all options to continue</p>
          {/if}
        </div>
      </div>
    </div>

    <!-- Checkout Modal -->
    {#if showCheckout}
      <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <div class="max-w-md w-full max-h-[90vh] overflow-y-auto">
          <CheckoutForm
            product={product}
            selectedVariation={selectedVariation}
            success={handleCheckoutSuccess}
            error={() => showCheckout = false}
            cancel={handleCheckoutCancel}
          />
        </div>
      </div>
    {/if}

    <!-- Order Confirmation Modal -->
    {#if showOrderConfirmation}
      <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <div class="max-w-md w-full">
          <OrderConfirmation
            orderId={orderId || 0}
            orderNumber={orderNumber}
            close={handleConfirmationClose}
            productId={product.id}
          />
        </div>
      </div>
    {/if}
  {/if}
</div>
