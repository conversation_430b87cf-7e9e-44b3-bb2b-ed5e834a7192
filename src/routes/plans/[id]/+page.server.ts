import type { PageServerLoad } from './$types';
import { wooCommerceService } from '$lib/server/woocommerce.server'; // Updated import path
import type { ProductVariation } from '$lib/services/woocommerce'; // Keep type import
import { env } from '$env/dynamic/private';

export const load: PageServerLoad = async ({ params }) => {
  const { id } = params;
  
  try {
    // Check if WooCommerce API is configured using private env vars
    const apiConfigured = !!env.WOOCOMMERCE_URL && 
                         !!env.WOOCOMMERCE_CONSUMER_KEY && 
                         !!env.WOOCOMMERCE_CONSUMER_SECRET;
    
    if (!apiConfigured) {
      console.warn('WooCommerce API not configured. Please set up your API credentials in .env');
      return {
        product: null,
        apiConfigured: false
      };
    }
    
    try {
      const product = await wooCommerceService.getProduct(parseInt(id));
      
      if (!product) {
        return {
          product: null,
          notFound: true
        };
      }
      
      // If this is a variable product, fetch its variations
      let variations: ProductVariation[] = [];
      if (product.type === 'variable' && product.variations && product.variations.length > 0) {
        variations = await wooCommerceService.getProductVariations(product.id);
      }
      
      return {
        product,
        variations,
        apiConfigured: true
      };
    } catch (apiErr) {
      console.error('WooCommerce API error:', apiErr);
      return {
        product: null,
        apiError: apiErr instanceof Error ? apiErr.message : 'Unknown API error'
      };
    }
  } catch (err) {
    console.error(`Error loading product with ID ${id}:`, err);
    return {
      product: null,
      error: err instanceof Error ? err.message : 'Unknown error'
    };
  }
};
