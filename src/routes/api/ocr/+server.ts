import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { env } from '$env/dynamic/private';

// Use the real OpenRouter AI API
const USE_MOCK_RESPONSE = false;

export const POST: RequestHandler = async ({ request }) => {
  try {
    // Get the image data from the request
    const { imageBase64 } = await request.json();
    
    if (!imageBase64) {
      return json({ success: false, error: 'No image data provided' }, { status: 400 });
    }
    
    // For testing, we'll use a mock response instead of calling the actual API
    if (USE_MOCK_RESPONSE) {
      console.log('Using mock response for OCR test');
      
      // Simulate a delay to mimic API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Randomly choose between valid and invalid receipt responses for testing
      const isValid = Math.random() > 0.3; // 70% chance of valid receipt
      
      // Return a mock response with the simplified format
      return json({
        success: true,
        output: {
          text: isValid ? 
            `I've analyzed this image and here's what I found:

{
  "is_payment_receipt": true,
  "transaction_id": "KBZ123456789",
  "payment_service": "KBZ Pay"
}` : 
            `I've analyzed this image and determined it is not a valid payment receipt.

{
  "is_payment_receipt": false,
  "transaction_id": null
}`
        }
      });
    }
    
    // Use the real OpenRouter AI API
    if (!env.OPENROUTER_API_KEY) {
      console.error('OpenRouter API key not found');
      return json({ 
        success: false, 
        error: 'API key not configured' 
      }, { status: 500 });
    }
    
    try {
      // Create a simplified prompt focused on transaction ID extraction
      const prompt = `  
      You are a specialized payment verification system. Your primary task is to extract the transaction ID/number from payment receipts.
      
      CRITICAL REQUIREMENT:
      - Output Only JSON object: Return solely the JSON object without any additional explanations or comments.
      
      Analyze this image and determine:

      Note: Payment receipt/invoice can sometimes be in English or Burmese.
      1. From the image find and extract the transaction ID or reference number. This is usually labeled as:
        - "Transaction ID"
        - "Reference Number"
        - "Transaction No"
        - "လုပ်ဆောင်မှုအမှတ်"
        - "လုပ်ဆောင်ချက်အိုင်ဒီ"
        - Or similar variations
      2. Find the name "Khin Maung Phyo" (Case Insensitive) in the receipt.

      Format your response as a simple JSON object:
      {
        "is_payment_receipt": true/false,
        "transaction_id": "the extracted transaction ID or null if not found",
      }
      (2) Conditions to check. Which are 
      1. You cannot find the name "Khin Maung Phyo" in the receipt
      2. You cannot find a transaction ID
      If any of these conditions are true than set is_payment_receipt to false and transaction_id to null.

      CRITICAL REQUIREMENT:
      - Output Only JSON object: Return solely the JSON object without any additional explanations or comments.
      `;
      
      // Call the OpenRouter AI API
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${env.OPENROUTER_API_KEY}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          "model": "google/gemini-2.0-flash-001",
          "messages": [
            {
              "role": "user",
              "content": [
                {
                  "type": "text",
                  "text": prompt
                },
                {
                  "type": "image_url",
                  "image_url": {
                    "url": `data:image/jpeg;base64,${imageBase64}`
                  }
                }
              ]
            }
          ]
        })
      });
      
      // Parse the response
      const responseData = await response.json();
      
      if (!response.ok) {
        console.error('OpenRouter API error:', responseData);
        return json({ 
          success: false, 
          error: responseData.error?.message || 'Error calling OpenRouter API' 
        }, { status: 500 });
      }
      
      // Extract the response text
      const responseText = responseData.choices[0]?.message?.content || '';
      
      console.log('OpenRouter AI response:', responseText);
      
      // Return the response
      return json({
        success: true,
        output: {
          text: responseText
        }
      });
    } catch (error) {
      console.error('OpenRouter AI API error:', error);
      return json({ 
        success: false, 
        error: error instanceof Error ? error.message : 'Error calling OpenRouter AI API' 
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error('Error processing OCR request:', error);
    return json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
};
