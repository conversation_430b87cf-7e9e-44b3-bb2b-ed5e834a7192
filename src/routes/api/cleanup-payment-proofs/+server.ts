import { json } from '@sveltejs/kit';
import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { promises as fs } from 'fs'; // Use asynchronous promises API
import path from 'path';
import { env } from '$env/dynamic/private'; // Use environment variable for upload directory

// Define the upload directory, defaulting to a path within the container if the env var isn't set during build
// This is primarily to satisfy the build process. At runtime in CapRover,
// the UPLOAD_DIR from CapRover's env vars should ideally be used if different,
// though this script is more about cleanup within the known container structure.
const ENV_UPLOAD_DIR = env.UPLOAD_DIR; // Access UPLOAD_DIR from the env object
const UPLOAD_DIR = ENV_UPLOAD_DIR || '/app/static/uploads';


// Maximum age of files to keep (1 day in milliseconds)
const MAX_AGE = 24 * 60 * 60 * 1000; // 24 hours

// Define a type for file processing errors
interface FileError {
    file: string;
    error: string;
}

export const GET: RequestHandler = async () => {
  try {
    // Check if directory exists asynchronously
    let dirExists = false;
    try {
        await fs.access(UPLOAD_DIR); // Check if directory is accessible
        dirExists = true;
    } catch (e: any) {
        if (e.code === 'ENOENT') {
            // Directory does not exist, which is fine for cleanup
            return json({
                success: true,
                message: 'Upload directory does not exist',
                deleted: 0
            });
        } else {
            // Other file system error
            console.error('Error checking upload directory:', e);
            throw new Error(`Error accessing upload directory: ${e.message}`);
        }
    }

    // Get current time
    const now = new Date().getTime();

    // Get all files in the directory asynchronously
    const files = await fs.readdir(UPLOAD_DIR);

    // Track deleted files
    let deletedCount = 0;
    let errors: FileError[] = []; // Explicitly type the errors array

    // Use Promise.all to process files concurrently
    await Promise.all(files.map(async (file) => {
      try {
        const filePath = path.join(UPLOAD_DIR, file);

        // Get file stats asynchronously
        const stats = await fs.stat(filePath);

        // Check if file is older than MAX_AGE
        const fileAge = now - stats.mtimeMs;

        if (fileAge > MAX_AGE) {
          // Delete the file asynchronously
          await fs.unlink(filePath);
          deletedCount++;
          console.log(`Deleted old payment proof: ${file}`);
        }
      } catch (fileError: any) {
        console.error(`Error processing file ${file}:`, fileError);
        errors.push({ file, error: fileError.message });
      }
    }));

    // Return success response
    return json({
      success: true,
      message: `Cleanup completed. Deleted ${deletedCount} files.`,
      deleted: deletedCount,
      errors: errors.length > 0 ? errors : undefined
    });
  } catch (error: any) {
    console.error('Error cleaning up payment proofs:', error);
    return json({
      success: false,
      error: error.message || 'Unknown error'
    }, { status: 500 });
  }
};
