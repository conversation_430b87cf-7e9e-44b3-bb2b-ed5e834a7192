import { json } from '@sveltejs/kit';
// Import the server-side WooCommerce service
import { wooCommerceService } from '$lib/server/woocommerce.server';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ url }) => {
  // Get query parameters
  const page = url.searchParams.get('page') || '1';
  const perPage = url.searchParams.get('per_page') || '20';
  
  try {
    // Build params object
    const params: Record<string, any> = {
      page: parseInt(page),
      per_page: parseInt(perPage)
    };
    
    // Fetch categories
    const categories = await wooCommerceService.getCategories(params);
    
    return json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return json({ error: 'Failed to fetch categories' }, { status: 500 });
  }
};
