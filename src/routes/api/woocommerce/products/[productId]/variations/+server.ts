import { wooCommerceService } from '$lib/server/woocommerce.server';
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ params }) => {
  const productIdString = params.productId;
  if (!productIdString) {
    return json({ error: 'Product ID is required' }, { status: 400 });
  }
  const productId = parseInt(productIdString, 10);

  if (isNaN(productId)) {
    return json({ error: 'Invalid product ID format' }, { status: 400 });
  }

  try {
    const variations = await wooCommerceService.getProductVariations(productId);
    // getProductVariations in wooCommerceService.server.ts returns ProductVariation[]
    // It returns an empty array [] if an error occurs or no variations are found.
    // So, we can directly return the result.
    // If the product itself doesn't exist, WooCommerce API might return an error
    // which getProductVariations handles and returns [].
    return json(variations);
  } catch (error) {
    // This catch block might be redundant if wooCommerceService.getProductVariations handles all errors
    // and returns [] as per its implementation. However, it's good for unexpected issues.
    console.error(`API error fetching variations for product ${productId}:`, error);
    return json({ error: 'Failed to fetch variations' }, { status: 500 });
  }
};
