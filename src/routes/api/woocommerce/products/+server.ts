import { json } from '@sveltejs/kit';
// Import the server-side WooCommerce service
import { wooCommerceService } from '$lib/server/woocommerce.server';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ url }) => {
  // Get query parameters
  const category = url.searchParams.get('category');
  const page = url.searchParams.get('page') || '1';
  const perPage = url.searchParams.get('per_page') || '10';
  
  try {
    // Build params object
    const params: Record<string, any> = {
      page: parseInt(page),
      per_page: parseInt(perPage)
    };
    
    // Add category if provided
    if (category) {
      params.category = parseInt(category);
    }
    
    // Fetch products
    const products = await wooCommerceService.getProducts(params);
    
    return json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    return json({ error: 'Failed to fetch products' }, { status: 500 });
  }
};
