import { json } from '@sveltejs/kit';
// Import the server-side WooCommerce service
import { wooCommerceService } from '$lib/server/woocommerce.server';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ params }) => {
  const { id } = params;
  
  try {
    // Fetch product by ID
    const product = await wooCommerceService.getProduct(parseInt(id));
    
    if (!product) {
      return json({ error: 'Product not found' }, { status: 404 });
    }
    
    return json(product);
  } catch (error) {
    console.error(`Error fetching product with ID ${id}:`, error);
    return json({ error: 'Failed to fetch product' }, { status: 500 });
  }
};
