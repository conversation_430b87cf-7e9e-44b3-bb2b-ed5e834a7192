import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { env } from '$env/dynamic/private';
import { findTransactionById, addTransaction } from '$lib/server/db';
import { v4 as uuidv4 } from 'uuid'; // Import uuid

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { imageBase64, orderId, fileName } = await request.json();

    if (!imageBase64) {
      return json({
        success: false,
        error: 'No image data provided',
        status: 'missing_image'
      }, { status: 400 });
    }

    if (!orderId) {
      return json({
        success: false,
        error: 'Order ID is required',
        status: 'missing_order_id'
      }, { status: 400 });
    }

    // AI Interaction and Database Operations
    try {
      // 1. Check for API Key existence on the server first
      if (!env.OPENROUTER_API_KEY) {
        console.warn('OpenRouter API key not configured on server. Bypassing AI verification for orderId:', orderId);
        const bypassTransactionId = `BYPASSED-NOKEY-${orderId}-${uuidv4()}`;
        return json({
          success: true,
          status: 'ai_verification_bypassed',
          transactionId: bypassTransactionId,
          paymentService: 'Bypassed - No API Key',
          message: 'Payment verification temporarily bypassed (server configuration). Your order will be manually reviewed.',
          debug_error: 'OpenRouter API key not configured on server.'
        }, { status: 200 });
      }

      const promptText = `
      You are a highly specialized AI assistant for processing payment receipts.
      Your ONLY task is to extract the transaction ID and verify a specific name from the provided image of a mobile payment receipt.
      You MUST respond ONLY with a JSON object. DO NOT include any other text, explanations, or markdown formatting outside the JSON.

      INSTRUCTIONS:
      1. Examine the image for a transaction ID or reference number. Look for labels like "Transaction ID", "Reference Number", "Transaction No", "လုပ်ဆောင်မှုအမှတ်", "လုပ်ဆောင်ချက်အိုင်ဒီ".
      2. Check if the name "Khin Maung Phyo" appears in the receipt (case insensitive).
      3. Based on your findings, generate a JSON object in the EXACT format specified below.

      JSON FORMAT:
      If a transaction ID is found AND the name "Khin Maung Phyo" is present:
      \`\`\`json
      {
        "is_payment_receipt": true,
        "transaction_id": "THE_EXTRACTED_ID"
      }
      \`\`\`

      If either a transaction ID is NOT found OR the name "Khin Maung Phyo" is NOT present:
      \`\`\`json
      {
        "is_payment_receipt": false,
        "transaction_id": null
      }
      \`\`\`

      Remember: Respond ONLY with the JSON object.
      `;

      let responseData;
      let aiApiResponse; // To store the response from the fetch call

      try {
        // 2. Attempt to call the OpenRouter API
        aiApiResponse = await fetch("https://openrouter.ai/api/v1/chat/completions", {
          method: "POST",
          headers: {
            "Authorization": `Bearer ${env.OPENROUTER_API_KEY}`,
            "Content-Type": "application/json",
            "HTTP-Referer": "https://ipmanvpn.com", // Replace with your actual site URL
            "X-Title": "IPMan VPN" // Replace with your actual app name
          },
          body: JSON.stringify({
            "model": "google/gemini-2.0-flash-001", // or your preferred model
            "messages": [
              { "role": "system", "content": "You are a strict JSON outputting assistant. Respond only with the requested JSON." },
              {
                "role": "user",
                "content": [
                  { "type": "text", "text": promptText },
                  { "type": "image_url", "image_url": { "url": `data:image/jpeg;base64,${imageBase64}` } }
                ]
              }
            ]
          })
        });

        if (aiApiResponse.ok) {
          responseData = await aiApiResponse.json();
          console.log('Successfully got AI response for orderId:', orderId);
        } else {
          // AI API call was not OK. Check for bypassable errors.
          const errorData = await aiApiResponse.json().catch(() => ({})); // Gracefully handle non-JSON error responses
          const errorMessage = errorData?.error?.message || `OpenRouter API error: ${aiApiResponse.status}`;
          console.error('OpenRouter API Error for orderId:', orderId, 'Status:', aiApiResponse.status, 'Message:', errorMessage, 'Data:', errorData);

          const isApiKeyOrCreditError =
            aiApiResponse.status === 401 || // Unauthorized (e.g., invalid key)
            aiApiResponse.status === 403 || // Forbidden (e.g., key disabled, IP block)
            aiApiResponse.status === 402 || // Payment Required (e.g., out of credits)
            (errorData?.error?.code === 'insufficient_quota') || // Specific error code from OpenRouter
            (typeof errorMessage === 'string' && ( // Check common phrases in error message
              errorMessage.toLowerCase().includes('api key expired') ||
              errorMessage.toLowerCase().includes('credit limit reached') ||
              errorMessage.toLowerCase().includes('invalid api key') ||
              errorMessage.toLowerCase().includes('insufficient funds')
            ));

          if (isApiKeyOrCreditError) {
            console.warn('OpenRouter API key/credit issue detected. Bypassing AI verification for orderId:', orderId);
            const bypassTransactionId = `BYPASSED-APIERR-${orderId}-${uuidv4()}`;
            return json({
              success: true,
              status: 'ai_verification_bypassed',
              transactionId: bypassTransactionId,
              paymentService: 'Bypassed - AI Error',
              message: 'Payment verification temporarily bypassed due to an AI service issue. Your order will be manually reviewed.',
              debug_error: `Bypass due to OpenRouter API error: ${errorMessage} (Status: ${aiApiResponse.status})`
            }, { status: 200 }); // Return 200 OK as the operation (order placement) can proceed
          }

          // For other non-bypassable AI API errors
          return json({
            success: false,
            error: 'Error တက်နေသည်။ နောက်တစ်ကြိမ်ထပ်မံကြိုးစားပါ။', // "Error. Please try again."
            status: 'ai_error',
            debug_error: `OpenRouter API Error: ${errorMessage} (Status: ${aiApiResponse.status})`
          }, { status: 500 });
        }
      } catch (fetchError: any) {
        // Catch errors during the fetch call itself (e.g., network issues)
        console.error('Failed to call OpenRouter API for orderId:', orderId, fetchError);
        // For now, network errors are treated as hard errors.
        // If bypass is desired for network errors too, similar logic as above could be added here.
        return json({
          success: false,
          error: 'Error တက်နေသည်။ နောက်တစ်ကြိမ်ထပ်မံကြိုးစားပါ။',
          status: 'ai_error',
          debug_error: `Failed to connect to AI service: ${fetchError.message}`
        }, { status: 500 });
      }

      // --- If we are here, AI call was successful (responseData is populated) ---
      const responseText = responseData.choices[0]?.message?.content || '';
      console.log('OpenRouter AI response content for orderId:', orderId, responseText);

      let extractedData;
      try {
        const jsonPattern = /\{\s*[\s\S]*?"is_payment_receipt"[\s\S]*?\}/g;
        let match = jsonPattern.exec(responseText);
        if (match && typeof match[0] === 'string') {
          try {
            extractedData = JSON.parse(match[0]);
            console.log('Successfully parsed JSON from AI response (specific pattern) for orderId:', orderId, extractedData);
          } catch (e) {
            console.warn('Failed to parse specific JSON pattern, trying general for orderId:', orderId, e);
          }
        }

        if (!extractedData) {
          const generalJsonPattern = /\{\s*[\s\S]*?\}/g;
          const matches = responseText.match(generalJsonPattern) || [];
          for (const matchStr of matches) {
            if (typeof matchStr === 'string') {
              try {
                const parsed = JSON.parse(matchStr);
                if ('is_payment_receipt' in parsed) {
                  extractedData = parsed;
                  console.log('Successfully parsed JSON from AI response (general pattern) for orderId:', orderId, extractedData);
                  break;
                }
              } catch (e) { /* continue */ }
            }
          }
        }
        
        if (!extractedData) {
            if (responseText.toLowerCase().includes('"is_payment_receipt": false') || responseText.toLowerCase().includes('not a payment receipt')) {
                extractedData = { is_payment_receipt: false, transaction_id: null, payment_service: null };
                console.log('AI indicated not a payment receipt via text, orderId:', orderId);
            } else {
                throw new Error('Could not extract structured payment information from AI response.');
            }
        }

      } catch (parseError: any) {
        console.error('Error parsing AI response for orderId:', orderId, parseError, 'Raw response text:', responseText);
        return json({
          success: false,
          error: 'Error တက်နေသည်။ နောက်တစ်ကြိမ်ထပ်မံကြိုးစားပါ။',
          status: 'parse_error',
          debug_error: `Failed to parse AI response: ${parseError.message}`
        }, { status: 500 });
      }

      if (!extractedData.is_payment_receipt || !extractedData.transaction_id) {
        console.log('AI determined not a valid receipt or no transaction ID for orderId:', orderId, extractedData);
        return json({
          success: false,
          error: 'The uploaded image does not appear to be a valid payment receipt or transaction ID is missing.',
          status: 'invalid_receipt',
          data: extractedData
        });
      }

      // Check for duplicate transaction ID
      const existingVerification = await findTransactionById(extractedData.transaction_id);
      if (existingVerification) {
        console.log('Duplicate transaction ID found for orderId:', orderId, extractedData.transaction_id);
        return json({
          success: false,
          error: 'This payment has already been used for another order.',
          status: 'duplicate_transaction',
          transactionId: extractedData.transaction_id,
          isDuplicate: true,
          existingOrderId: existingVerification.order_id
        });
      }

      // Store the verified transaction
      const newDbEntry = await addTransaction({
        transaction_id: extractedData.transaction_id,
        order_id: orderId, // Use the orderId passed in the request
        payment_service: extractedData.payment_service || 'Unknown', // payment_service might not always be in AI response
        verification_status: 'verified',
        image_path: fileName || null // Store original filename if available
      });

      if (!newDbEntry) {
        console.error('Failed to save transaction to database for orderId:', orderId, extractedData.transaction_id);
        return json({
          success: false,
          error: 'Error တက်နေသည်။ နောက်တစ်ကြိမ်ထပ်မံကြိုးစားပါ။',
          status: 'database_error',
          debug_error: 'Failed to save transaction verification record'
        }, { status: 500 });
      }

      console.log('Payment verified and recorded for orderId:', orderId, extractedData.transaction_id);
      return json({
        success: true,
        transactionId: extractedData.transaction_id,
        paymentService: extractedData.payment_service || 'Unknown',
        status: 'verified',
        message: 'Payment successfully verified.',
        verificationId: newDbEntry.id
      });

    } catch (processError: any) {
      // Catch errors from the AI interaction block (e.g., unexpected issues not caught by inner try-catch)
      console.error('AI verification process error for orderId:', orderId, processError);
      return json({
        success: false,
        error: 'Error တက်နေသည်။ နောက်တစ်ကြိမ်ထပ်မံကြိုးစားပါ။',
        status: 'ai_error',
        debug_error: processError instanceof Error ? processError.message : 'Unknown error in AI verification process'
      }, { status: 500 });
    }

  } catch (requestError: any) {
    // Catch errors in initial request processing (e.g., JSON parsing of request body, missing fields)
    console.error('Error processing payment verification request:', requestError);
    return json({
      success: false,
      error: 'Failed to process request. Ensure all required fields are provided.',
      status: 'request_error', // Custom status for this type of error
      debug_error: requestError instanceof Error ? requestError.message : 'Unknown error processing request'
    }, { status: 400 }); // Bad request if initial parsing fails or fields are missing
  }
};
