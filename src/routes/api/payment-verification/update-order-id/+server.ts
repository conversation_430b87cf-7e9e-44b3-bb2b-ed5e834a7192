import { json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';
import { updateVerificationOrderId } from '$lib/server/db';

export const POST = async ({ request }: RequestEvent) => {
  try {
    const { verificationId, orderId } = await request.json();

    if (!verificationId || !orderId) {
      return json({
        success: false,
        error: 'Both verificationId and orderId are required'
      }, { status: 400 });
    }

    const updated = await updateVerificationOrderId(verificationId, orderId);

    if (!updated) {
      return json({
        success: false,
        error: 'Failed to update verification record'
      }, { status: 500 });
    }

    return json({
      success: true,
      message: 'Verification record updated successfully'
    });
  } catch (error) {
    console.error('Error updating verification order ID:', error);
    return json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}; 