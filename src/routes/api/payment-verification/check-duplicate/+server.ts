import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { findTransactionById } from '$lib/server/db';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { transactionId } = await request.json();

    if (!transactionId) {
      return json({
        success: false,
        error: 'Transaction ID is required'
      }, { status: 400 });
    }

    // Check if transaction ID exists in database
    const existingVerification = await findTransactionById(transactionId);

    return json({
      success: true,
      isDuplicate: !!existingVerification,
      existingOrderId: existingVerification ? existingVerification.order_id : null
    });

  } catch (error) {
    console.error('Error checking duplicate transaction:', error);
    return json({
      success: false,
      error: 'Failed to check for duplicate transaction'
    }, { status: 500 });
  }
};
