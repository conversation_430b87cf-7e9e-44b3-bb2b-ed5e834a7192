import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import {
  hashPassword,
  generateSessionToken,
  getAdminPasswordHash,
  createSession,
  deleteSession,
  getActiveSessionCount
} from '$lib/server/auth.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { password } = await request.json();
    
    if (!password) {
      return json({
        success: false,
        error: 'Password is required'
      }, { status: 400 });
    }
    
    // Hash the provided password and compare
    const providedPasswordHash = hashPassword(password);
    const correctPasswordHash = getAdminPasswordHash();
    
    if (providedPasswordHash === correctPasswordHash) {
      // Generate session token
      const token = generateSessionToken();
      const expires = Date.now() + (24 * 60 * 60 * 1000); // 24 hours

      // Store session
      createSession(token);

      console.log(`Admin login successful. Active sessions: ${getActiveSessionCount()}`);

      return json({
        success: true,
        token,
        expires
      });
    } else {
      // Add delay to prevent brute force attacks
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('Admin login failed - invalid password');
      
      return json({
        success: false,
        error: 'Invalid password'
      }, { status: 401 });
    }
    
  } catch (error) {
    console.error('Admin authentication error:', error);
    return json({
      success: false,
      error: 'Authentication failed'
    }, { status: 500 });
  }
};

// Verify session token (for API endpoints) - moved to separate utility
// This function is now available via the activeSessions Map

// Logout endpoint
export const DELETE: RequestHandler = async ({ request }) => {
  try {
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (token) {
      const deleted = deleteSession(token);
      if (deleted) {
        console.log(`Admin session logged out. Active sessions: ${getActiveSessionCount()}`);
      }
    }

    return json({ success: true });
  } catch (error) {
    console.error('Admin logout error:', error);
    return json({
      success: false,
      error: 'Logout failed'
    }, { status: 500 });
  }
};
