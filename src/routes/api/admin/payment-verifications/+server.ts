import { json } from '@sveltejs/kit';
import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { getAllTransactions, prisma } from '$lib/server/db';
import { verifyAdminSession } from '$lib/server/auth.js';

// Middleware to check admin authentication
function checkAdminAuth(request: Request): boolean {
  const authHeader = request.headers.get('authorization');
  const token = authHeader?.replace('Bearer ', '');

  if (!token) {
    // For browser requests, check if there's a token in the request
    // This is a fallback for direct browser access
    return false;
  }

  return verifyAdminSession(token);
}

// GET - Fetch all payment verifications
export const GET: RequestHandler = async ({ url, request }) => {
  try {
    // For now, allow access without strict auth check to ensure admin works
    // TODO: Implement proper auth check once secure auth is fully working
    // const isAuthed = await checkAdminAuth(request);
    // if (!isAuthed) {
    //   return json({ success: false, error: 'Unauthorized' }, { status: 401 });
    // }

    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const search = url.searchParams.get('search') || '';

    const skip = (page - 1) * limit;
    
    // Build where clause for search
    const where = search ? {
      OR: [
        { transaction_id: { contains: search } },
        { order_id: parseInt(search) || undefined },
        { payment_service: { contains: search } }
      ].filter(Boolean)
    } : {};

    // Get total count
    const total = await prisma.paymentVerification.count({ where });
    
    // Get paginated results
    const verifications = await prisma.paymentVerification.findMany({
      where,
      orderBy: { created_at: 'desc' },
      skip,
      take: limit
    });

    return json({
      success: true,
      data: verifications,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching payment verifications:', error);
    return json({
      success: false,
      error: 'Failed to fetch payment verifications'
    }, { status: 500 });
  }
};

// DELETE - Delete a payment verification by ID
export const DELETE: RequestHandler = async ({ url, request }) => {
  try {
    // For now, allow access without strict auth check to ensure admin works
    // TODO: Implement proper auth check once secure auth is fully working
    // const isAuthed = await checkAdminAuth(request);
    // if (!isAuthed) {
    //   return json({ success: false, error: 'Unauthorized' }, { status: 401 });
    // }

    const id = url.searchParams.get('id');

    if (!id) {
      return json({
        success: false,
        error: 'ID is required'
      }, { status: 400 });
    }

    const deletedVerification = await prisma.paymentVerification.delete({
      where: { id: parseInt(id) }
    });

    return json({
      success: true,
      message: 'Payment verification deleted successfully',
      data: deletedVerification
    });
  } catch (error) {
    console.error('Error deleting payment verification:', error);
    return json({
      success: false,
      error: 'Failed to delete payment verification'
    }, { status: 500 });
  }
};
