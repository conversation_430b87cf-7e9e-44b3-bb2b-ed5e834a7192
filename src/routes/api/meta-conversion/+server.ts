import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
// Import sensitive token from private environment variables (server-side only)
import { env as privateEnv } from '$env/dynamic/private';
// Import public pixel ID from public environment variables
import { env as publicEnv } from '$env/dynamic/public';
import crypto from 'crypto';

/**
 * Meta Graph API Implementation
 *
 * Current version: v22.0 (as of January 2025)
 *
 * Note: Meta API versions typically have a 2-year lifecycle.
 * This implementation should be reviewed and updated approximately every 6 months
 * to ensure compatibility with the latest API version.
 *
 * For more information, visit: https://developers.facebook.com/docs/graph-api/changelog/
 */

// Define types for the Meta Conversion API
interface EventParams {
  external_id?: string;
  _fbp?: string;
  _fbc?: string;
  currency?: string;
  value?: number;
  [key: string]: any;
}

interface EventData {
  eventName: string;
  eventParams: EventParams;
  userAgent: string;
  url: string;
  eventTime: number;
}

export const POST: RequestHandler = async ({ request, getClientAddress }) => {
  try {
    const data = await request.json() as EventData;
    const { eventName, eventParams, userAgent, url, eventTime } = data;

    // Get client IP address
    const ipAddress = getClientAddress();

    // Generate a unique event ID
    const eventId = crypto.randomUUID();

    // Extract external_id if available
    const externalId = eventParams?.external_id || '';

    // Define the type for userData to include optional fbp and fbc
    interface UserData {
      client_ip_address: string;
      client_user_agent: string;
      external_id: string;
      fbp?: string;
      fbc?: string;
    }

    // Create the user data object for the Conversion API
    const userData: UserData = {
      client_ip_address: ipAddress,
      client_user_agent: userAgent,
      external_id: externalId,
    };

    // Get the fbp and fbc cookies if available
    if (eventParams?._fbp) {
      userData.fbp = eventParams._fbp;
      console.log('FBP cookie found:', eventParams._fbp);
    } else {
      console.warn('FBP cookie not found in event params');
    }

    if (eventParams?._fbc) {
      userData.fbc = eventParams._fbc;
      console.log('FBC cookie found:', eventParams._fbc);
    } else {
      console.warn('FBC cookie not found in event params');
    }

    // Remove Meta-specific fields from eventParams before sending to API
    const cleanedParams = { ...eventParams };
    delete cleanedParams.external_id;
    delete cleanedParams._fbp;
    delete cleanedParams._fbc;

    // Create the custom data object for the Conversion API
    const customData = {
      ...cleanedParams,
      currency: eventParams?.currency || 'MMK',
      value: eventParams?.value || 0,
    };

    // Define the type for the event object
    interface ConversionEvent {
      event_name: string;
      event_time: number;
      event_id: string;
      event_source_url: string;
      action_source: 'website' | 'app' | 'chat' | 'email' | 'other' | 'phone_call' | 'system_generated';
      user_data: UserData;
      custom_data: Record<string, any>;
    }

    // Create the event object for the Conversion API
    const event: ConversionEvent = {
      event_name: eventName,
      event_time: eventTime,
      event_id: eventId,
      event_source_url: url,
      action_source: 'website',
      user_data: userData,
      custom_data: customData,
    };

    // Define the type for the request body
    interface ConversionApiRequestBody {
      data: ConversionEvent[];
      access_token: string;
    }

    // Create the request body for the Conversion API
    const requestBody: ConversionApiRequestBody = {
      data: [event],
      access_token: privateEnv.META_CONVERSION_API_TOKEN, // Use the server-side private token
    };

    console.log('Sending to Meta Conversion API:', JSON.stringify({
      data: requestBody.data, // Log data structure but not the token
      // access_token is intentionally omitted from logs
    }, null, 2));

    // Debug info
    console.log('Event details:', {
      name: eventName,
      external_id: externalId,
      ip: ipAddress,
      user_agent: userAgent,
      url: url,
      fbp: userData.fbp || 'Not provided',
      fbc: userData.fbc || 'Not provided'
    });

    // Log consistency warning if external_id format doesn't match expected pattern
    if (externalId && !externalId.startsWith('ext_')) {
      console.warn('External ID may not be in the expected format:', externalId);
    }

    // Send the event to the Conversion API using the latest v22.0 version
    const response = await fetch(
      `https://graph.facebook.com/v22.0/${publicEnv.PUBLIC_META_PIXEL_ID}/events`, // Use public pixel ID
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      }
    );

    // Define the type for the API response
    interface ConversionApiResponse {
      events_received?: number;
      messages?: string[];
      fbtrace_id?: string;
      error?: {
        message: string;
        type: string;
        code: number;
        error_subcode?: number;
        fbtrace_id: string;
      };
    }

    // Parse the response
    const responseData = await response.json() as ConversionApiResponse;

    // Log the response
    console.log('Meta Conversion API response:', responseData);

    // Check for API version-related errors
    if (responseData.error && (
      responseData.error.message.includes('version') ||
      responseData.error.message.includes('deprecated') ||
      responseData.error.message.includes('unsupported')
    )) {
      console.error('Meta API version error detected:', responseData.error.message);
      // You might want to send an alert to your team when this happens
    }

    // Return the response
    return json({
      success: responseData.error ? false : true,
      message: responseData.error ? `Error: ${responseData.error.message}` : 'Event sent to Meta Conversion API',
      response: responseData,
    });
  } catch (error) {
    console.error('Error sending to Meta Conversion API:', error);
    return json({
      success: false,
      message: 'Failed to send event to Meta Conversion API',
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
};
