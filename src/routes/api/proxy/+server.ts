import { error, json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

// Set up a server-side proxy for external API requests to avoid CORS issues
export const GET: RequestHandler = async ({ url, fetch }) => {
  try {
    // Extract the target URL from the query parameter
    const targetUrl = url.searchParams.get('url');
    
    if (!targetUrl) {
      throw error(400, 'Missing required "url" parameter');
    }
    
    // Validate the URL to ensure it's properly formed
    try {
      new URL(targetUrl);
    } catch (err) {
      throw error(400, 'Invalid URL provided');
    }
    
    console.log(`Proxying request to: ${targetUrl}`);
    
    // Make the request to the target URL
    const response = await fetch(targetUrl);
    
    if (!response.ok) {
      throw error(response.status, `Target API returned ${response.status}: ${response.statusText}`);
    }
    
    // Get the response data as JSON
    const data = await response.json();
    
    // Return the proxied response
    return json(data);
  } catch (err) {
    console.error('Proxy error:', err);
    
    if (err instanceof Error) {
      throw error(500, err.message);
    }
    
    throw error(500, 'An unknown error occurred');
  }
};