import { json, type RequestEvent } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
// Import the server-side WooCommerce service
import { wooCommerceService } from '$lib/server/woocommerce.server';
import { updateVerificationOrderId } from '$lib/server/db';
import { withErrorHandling, ValidationError } from '$lib/utils/error-handler';

const handleCreateOrder = async ({ request }: RequestEvent) => {
  const { orderData, verificationId } = await request.json();

  // Validate the incoming data
  if (!orderData || !orderData.billing || !orderData.billing.first_name || !orderData.billing.phone) {
    throw new ValidationError('Missing required fields: name and phone are required');
  }
  if (!orderData.line_items || !orderData.line_items.length) {
    throw new ValidationError('No products in order');
  }
  if (!verificationId) {
    throw new ValidationError('Verification ID is required to create an order');
  }

  // Create the WooCommerce order
  const order = await wooCommerceService.createOrder(orderData);
  if (!order) {
    // Note: In a real-world scenario, you might want to add logic here
    // to "clean up" the verification record if the order fails.
    throw new Error('Failed to create WooCommerce order');
  }

  // Reliably link the verification record to the new order on the server
  try {
    await updateVerificationOrderId(verificationId, order.id);
    console.log(`Successfully linked verification record ${verificationId} to order ${order.id}`);
  } catch (dbError) {
    // If this fails, the order was still created, but the link failed.
    // This is a critical issue that should be logged for manual intervention.
    console.error(`CRITICAL: Failed to link verificationId ${verificationId} to orderId ${order.id}`, dbError);
    // You might want to send an alert to an admin here.
  }

  // Return the successful response
  return json({
    success: true,
    order
  });
};

export const POST = withErrorHandling(handleCreateOrder);
