import { json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';
import { v4 as uuidv4 } from 'uuid';
import { env } from '$env/dynamic/private';
import { promises as fs } from 'fs';
import path from 'path';
import { FILE_UPLOAD } from '$lib/config/constants';
import { withErrorHandling, validateFileUpload, ValidationError } from '$lib/utils/error-handler';

// Get upload directory from environment variable
const ENV_UPLOAD_DIR = env.UPLOAD_DIR;
const UPLOAD_DIR = ENV_UPLOAD_DIR || '/app/static/uploads/payment-proofs';
const uploadDirPath = UPLOAD_DIR;

const handleUpload = async ({ request }: RequestEvent) => {
  // Ensure upload directory exists before processing the file
  try {
    await fs.mkdir(uploadDirPath, { recursive: true });
  } catch (mkdirError) {
    console.error(`Critical error: Failed to create upload directory ${uploadDirPath}:`, mkdirError);
    throw new ValidationError('Server configuration error for file uploads');
  }

  // Get the form data from the request
  const formData = await request.formData();
  const file = formData.get('file') as File;

  // Validate the file using the new utility
  validateFileUpload(file, {
    maxSizeBytes: FILE_UPLOAD.MAX_SIZE_BYTES,
    allowedTypes: FILE_UPLOAD.ALLOWED_TYPES
  });

  // Generate a unique filename
  const fileExtension = path.extname(file.name);
  const uniqueFilename = `payment-proof-${uuidv4()}${fileExtension}`;
  const fullFilePath = path.join(uploadDirPath, uniqueFilename);

  // Convert file to ArrayBuffer and save it asynchronously
  const arrayBuffer = await file.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);

  try {
    await fs.writeFile(fullFilePath, buffer);
    console.log(`File saved to: ${fullFilePath}`);

    // Log the file details
    console.log('File details:', {
      name: file.name,
      type: file.type,
      size: file.size,
      path: fullFilePath
    });

    // Generate URL for the file
    const fileUrl = `/uploads/payment-proofs/${uniqueFilename}`;

    // Return success response
    return json({
      success: true,
      message: 'File uploaded successfully',
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      fileUrl: fileUrl
    });
  } catch (fileWriteError) {
    console.error('Error writing file:', fileWriteError);
    throw new ValidationError('Failed to save file');
  }
};

export const POST = withErrorHandling(handleUpload);
