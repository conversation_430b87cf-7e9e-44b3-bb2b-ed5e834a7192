<script lang="ts">
  import Typography from '$lib/components/Typography.svelte';
  import SectionHeading from '$lib/components/SectionHeading.svelte';
  import { typography } from '$lib/styles/typography';
</script>

<div class="container mx-auto px-4 py-12">
  <SectionHeading 
    title="Typography System" 
    subtitle="A guide to using the centralized typography system for consistent text styling across the application"
    centered={true}
  />
  
  <div class="max-w-4xl mx-auto">
    <div class="mb-12">
      <Typography variant="h3" className="mb-4">Why Use a Typography System?</Typography>
      <Typography variant="p" className="mb-4">
        A centralized typography system offers several benefits:
      </Typography>
      <ul class="list-disc pl-6 space-y-2 mb-6">
        <li class={typography.paragraph.base}>
          <strong>Consistency:</strong> Ensures text looks the same across all pages and components
        </li>
        <li class={typography.paragraph.base}>
          <strong>Maintainability:</strong> Change text styles in one place instead of throughout the codebase
        </li>
        <li class={typography.paragraph.base}>
          <strong>Responsive:</strong> Built-in responsive sizing for different screen sizes
        </li>
        <li class={typography.paragraph.base}>
          <strong>Accessibility:</strong> Ensures proper text hierarchy and readability
        </li>
      </ul>
    </div>
    
    <div class="mb-12">
      <Typography variant="h3" className="mb-4">How to Use the Typography System</Typography>
      
      <div class="mb-8">
        <Typography variant="h4" className="mb-2">Method 1: Using the Typography Component</Typography>
        <Typography variant="p" className="mb-4">
          The easiest way to use the typography system is with the Typography component:
        </Typography>
        <div class="bg-gray-100 p-4 rounded-md mb-4">
          <pre class="text-sm overflow-x-auto">
{`<Typography variant="h1">This is a heading</Typography>
<Typography variant="p">This is a paragraph</Typography>
<Typography variant="lead">This is a lead paragraph</Typography>`}
          </pre>
        </div>
        <Typography variant="p">
          The Typography component supports these variants:
        </Typography>
        <ul class="list-disc pl-6 space-y-1 mb-4">
          <li class={typography.paragraph.small}>h1, h2, h3, h4, h5, h6 - Heading styles</li>
          <li class={typography.paragraph.small}>p - Standard paragraph</li>
          <li class={typography.paragraph.small}>lead - Larger, more prominent paragraph</li>
          <li class={typography.paragraph.small}>small - Smaller text</li>
          <li class={typography.paragraph.small}>tiny - Very small text</li>
        </ul>
      </div>
      
      <div class="mb-8">
        <Typography variant="h4" className="mb-2">Method 2: Using Typography Classes Directly</Typography>
        <Typography variant="p" className="mb-4">
          You can also apply the typography classes directly to HTML elements:
        </Typography>
        <div class="bg-gray-100 p-4 rounded-md mb-4">
          <pre class="text-sm overflow-x-auto">
{`<h2 class={typography.heading.h2}>This is a heading</h2>
<p class={typography.paragraph.base}>This is a paragraph</p>
<span class={typography.special.price}>$19.99</span>`}
          </pre>
        </div>
      </div>
      
      <div class="mb-8">
        <Typography variant="h4" className="mb-2">Method 3: Using Helper Components</Typography>
        <Typography variant="p" className="mb-4">
          For common patterns, we've created helper components like SectionHeading:
        </Typography>
        <div class="bg-gray-100 p-4 rounded-md mb-4">
          <pre class="text-sm overflow-x-auto">
{`<SectionHeading 
  title="My Section Title" 
  subtitle="Optional subtitle text goes here"
  centered={true}
/>`}
          </pre>
        </div>
      </div>
    </div>
    
    <div class="mb-12">
      <Typography variant="h3" className="mb-4">How to Modify the Typography System</Typography>
      <Typography variant="p" className="mb-4">
        To change typography styles across the entire site, edit the <code class="bg-gray-100 px-1 py-0.5 rounded">src/lib/styles/typography.ts</code> file.
        All components using these styles will automatically update.
      </Typography>
      <div class="bg-gray-100 p-4 rounded-md mb-4">
        <pre class="text-sm overflow-x-auto">
{`// Example: Change h1 size in typography.ts
export const headingStyles = {
  h1: 'text-5xl md:text-7xl font-heading font-bold text-secondary', // Updated size
  // ...other heading styles
};`}
        </pre>
      </div>
      <Typography variant="p">
        This is much more efficient than finding and updating text styles throughout the codebase!
      </Typography>
    </div>
    
    <div class="p-6 border border-primary/20 rounded-lg bg-primary/5">
      <Typography variant="h4" className="mb-2">Typography Demo</Typography>
      <Typography variant="p" className="mb-4">
        To see all available typography styles in action, visit the <a href="/typography-demo" class={typography.special.link}>Typography Demo page</a>.
      </Typography>
    </div>
  </div>
</div>
