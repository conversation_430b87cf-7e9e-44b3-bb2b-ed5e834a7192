<script lang="ts">
  import { onMount } from 'svelte';
  import { trackEvent, MetaPixelEvent } from '$lib/services/meta-pixel';
  import Typography from '$lib/components/Typography.svelte'; // Import Typography component

  // Define types
  interface App {
    name: string;
    icon: string;
    downloadUrl: string;
  }

  interface Platform {
    id: string;
    name: string;
    icon: string;
    description: string;
    downloadUrl?: string;
    tutorialUrl?: string;
    primaryApp?: App;
    isIOS?: boolean;
  }

  // Platform data (using example URLs for now)
  const platforms: Platform[] = [
    {
      id: 'android',
      name: 'Android',
      icon: '/images/platforms/android.svg',
      description: 'IPman VPN Android ဗားရှင်းကို ဒေါင်းလုဒ်ဆွဲပြီး 1GB Free ရယူလိုက်ပါ။',
      tutorialUrl: '#', // Replace with actual URL
      primaryApp: {
        name: 'IPman VPN',
        icon: '/images/Logo.svg',
        downloadUrl: 'https://intarnad.com/IPman.apk' // Replace with actual URL
      }
    },
    {
      id: 'windows',
      name: 'Windows',
      icon: '/images/platforms/windows.svg',
      description: 'IPman VPN Windows ဗားရှင်းကို ဒေါင်းလုဒ်ဆွဲပြီး 1GB Free ရယူလိုက်ပါ။',
      tutorialUrl: '#', // Replace with actual URL
      primaryApp: {
        name: 'IPman VPN',
        icon: '/images/Logo.svg',
        downloadUrl: 'https://intarnad.com/IPman.exe' // Replace with actual URL
      }
    },
    {
      id: 'macos',
      name: 'macOS',
      icon: '/images/platforms/macos.svg',
      description: 'IPman VPN macOS ဗားရှင်းကို ဒေါင်းလုဒ်ဆွဲပြီး 1GB Free ရယူလိုက်ပါ။',
      tutorialUrl: '#', // Replace with actual URL
      primaryApp: {
        name: 'IPman VPN',
        icon: '/images/Logo.svg',
        downloadUrl: 'https://intarnad.com/IPman.dmg' // Replace with actual URL
      }
    },
    {
      id: 'ios',
      name: 'iOS',
      icon: '/images/platforms/ios.svg',
      description: 'IOS အသုံးပြုသူများအတွက် IPမန်း VPN App မရှိသေးပါ။ Free 1GB ရယူခွင့်မရှိသေးသော်လည်း V2BoX, Hiddify အစရှိတဲ့ Client App များထဲမှ တစ်ခု ကြိုက်နှစ်သက်ရာ တစ်ခုရွေးချယ်ပြီး IPမန်း VPN ဝယ်ယူကာ အသုံးပြုနိုင်မှာဖြစ်ပါတယ်။',
      isIOS: true
    }
  ];

  // Active platform tab
  let activeTab: string = 'android';
  let activePlatform: Platform = platforms[0];

  // Set active platform
  function setActiveTab(id: string): void {
    activeTab = id;
    activePlatform = platforms.find(p => p.id === id) || platforms[0];
  }

  // Device detection function
  function detectUserDevice(): string {
    if (typeof navigator !== 'undefined') {
      const userAgent = navigator.userAgent.toLowerCase();
      
      // Check for iOS devices (iPhone, iPad)
      if (/iphone|ipad|ipod/.test(userAgent)) {
        return 'ios';
      }
      
      // Check for Android devices
      if (/android/.test(userAgent)) {
        return 'android';
      }
      
      // Check for macOS
      if (/macintosh|mac os x/.test(userAgent) && !/iphone|ipad|ipod/.test(userAgent)) {
        return 'macos';
      }
      
      // Check for Windows
      if (/windows nt|win32/.test(userAgent)) {
        return 'windows';
      }
    }
    
    // Default to Android if can't detect
    return 'android';
  }

  // Track download event
  function trackDownload(appName: string, platformId: string): void {
    trackEvent(MetaPixelEvent.LEAD, {
      content_name: `${appName} Download`,
      content_category: 'App Download',
      content_type: platformId
    });
  }

  // Track ViewContent event when the component mounts
  onMount(() => {
    // Auto-detect device and set active tab
    const detectedDeviceId = detectUserDevice();
    setActiveTab(detectedDeviceId);
    
    trackEvent(MetaPixelEvent.VIEW_CONTENT, {
      content_type: 'download',
      content_name: 'Download Page',
      content_category: 'Downloads'
    });
  });
</script>

<div class="pt-20 md:pt-24 pb-16 bg-gradient-to-br from-primary/5 via-white to-accent/10">
  <div class="container mx-auto px-4">
    <div class="max-w-3xl mx-auto text-center mb-12 md:mb-16">
      <Typography variant="h1" className="mb-4">
        IPman VPN ဒေါင်းလုဒ်
      </Typography>
      <Typography variant="lead" className="mb-8">
        IPman VPN ကို ဒေါင်းလုဒ်ဆွဲပြီး 1GB Free ရယူလိုက်ပါ။ Android, Windows နှင့် macOS များအတွက် ရရှိနိုင်ပါပြီ။
      </Typography>
    </div>

    <!-- Platform Tabs -->
    <div class="flex flex-wrap justify-center mb-8 md:mb-10 border-b border-gray-200">
      {#each platforms as platform}
        <button
          class="px-4 py-3 md:px-6 font-heading text-base md:text-lg transition-all duration-300 relative -mb-px {activeTab === platform.id ? 'text-primary border-b-2 border-primary' : 'text-secondary/60 hover:text-secondary/80 border-b-2 border-transparent'}"
          on:click={() => setActiveTab(platform.id)}
        >
          <div class="flex items-center gap-2">
            <img src={platform.icon} alt={platform.name} class="w-5 h-5 md:w-6 md:h-6" />
            <span>{platform.name}</span>
          </div>
        </button>
      {/each}
    </div>

    <!-- Platform Content -->
    <div class="max-w-4xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
      {#if activePlatform.isIOS}
        <!-- iOS Content -->
        <div class="p-6 md:p-10">
           <Typography variant="h2" className="mb-4 !text-3xl md:!text-4xl">
             {activePlatform.name} အတွက် IPman VPN
           </Typography>
           <Typography variant="p" className="mb-6"> <!-- Changed base to p -->
             {activePlatform.description}
           </Typography>

           <div class="bg-blue-50 p-4 md:p-6 rounded-lg border border-blue-100 mb-6">
             <Typography variant="h5" as="h3" className="mb-3 flex items-center !font-bold">
               <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                 <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
               </svg>
               အသုံးပြုနိုင်သော Client Apps များ
             </Typography>
             <ul class="space-y-2 pl-8">
               {#each ['V2Box', 'Hiddify', 'Streisand'] as appName}
                 <li class="flex items-center">
                   <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                  </svg>
                  <Typography variant="p">{appName}</Typography> <!-- Changed base to p -->
                </li>
              {/each}
             </ul>
           </div>

           <div class="bg-yellow-50 p-4 md:p-6 rounded-lg border border-yellow-200 mb-8">
              <Typography variant="h5" as="h3" className="mb-2 !font-bold">သတိပြုရန်</Typography>
              <ul class="list-disc pl-5 space-y-1">
                <li><Typography variant="small">iOS အတွက် IPman VPN App မရှိသေးပါ</Typography></li>
                <li><Typography variant="small">Free 1GB ရယူခွင့် မရှိသေးပါ</Typography></li>
                <li><Typography variant="small">Client App တစ်ခုခုဖြင့် IPman VPN ဝယ်ယူကာ အသုံးပြုနိုင်မှာဖြစ်ပါတယ်</Typography></li>
              </ul>
            </div>

           <div>
             <a
               href="/plans"
               class="inline-block px-6 py-3 bg-primary text-white font-heading rounded-md hover:bg-primary-dark transition-colors duration-300 text-base md:text-lg"
             >
               Plans ကြည့်ရန်
             </a>
           </div>
         </div>
      {:else}
        <!-- Regular Platform Content -->
        <div class="p-6 md:p-10 grid md:grid-cols-2 gap-8 items-center">
          <div>
            <Typography variant="h2" className="mb-4 !text-3xl md:!text-4xl">
              {activePlatform.name} အတွက် IPman VPN
            </Typography>
            <Typography variant="p" className="mb-6"> <!-- Changed base to p -->
              {activePlatform.description}
            </Typography>

            <!-- Primary App -->
            <div class="mb-6">
              <!-- <Typography variant="h5" as="h3" className="mb-3 !font-bold">IPman VPN App</Typography> -->
              <div class="bg-gray-50 p-4 rounded-lg flex flex-col sm:flex-row items-center gap-4 mb-3 border border-gray-200">
                <img src={activePlatform.primaryApp?.icon || '/images/Logo.svg'} alt="IPman VPN" class="w-12 h-12 rounded-md shrink-0" />
                <div class="flex-grow text-center sm:text-left">
                  <Typography variant="h6" as="h4" className="!font-bold">{activePlatform.primaryApp?.name || 'IPman VPN'}</Typography>
                  <Typography variant="small" className="!text-secondary/60">Official IPman VPN App</Typography>
                </div>
                <a
                  href={activePlatform.primaryApp?.downloadUrl || '#'}
                  class="px-5 py-2.5 bg-primary text-white font-heading rounded-md hover:bg-primary-dark transition-colors duration-300 text-base shrink-0 w-full sm:w-auto text-center"
                  on:click={() => trackDownload(activePlatform.primaryApp?.name || 'IPman VPN', activePlatform.id)}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Download
                </a>
              </div>
              <!-- <a
                href={activePlatform.tutorialUrl}
                class="inline-flex items-center text-primary hover:underline text-sm"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                တပ်ဆင်နည်း ဗီဒီယိုကြည့်ရန်
              </a> -->
            </div>
          </div>

          <div class="bg-primary/5 p-4 md:p-6 rounded-lg border border-primary/10">
            <Typography variant="h5" as="h3" className="mb-3 !font-bold">Free 1GB ရယူနည်း</Typography>
            <ol class="space-y-2">
             <li class="flex items-start">
               <span class="mr-2 text-primary font-bold">1.</span>
               <Typography variant="p">IPman VPN App ကို ဒေါင်းလုဒ်ဆွဲပါ</Typography> <!-- Changed base to p -->
             </li>
              <li class="flex items-start">
               <span class="mr-2 text-primary font-bold">2.</span>
               <Typography variant="p">App ကို ဖွင့်ပြီး "Free 1GB" ခလုတ်ကို နှိပ်ပါ</Typography> <!-- Changed base to p -->
             </li>
              <li class="flex items-start">
               <span class="mr-2 text-primary font-bold">3.</span>
               <Typography variant="p">ခလုတ်တစ်ချက်နှိပ်ပြီး အသုံးပြုနိုင်ပါပြီ</Typography> <!-- Changed base to p -->
             </li>
            </ol>
          </div>
        </div>
      {/if}
    </div>
  </div>
</div>
