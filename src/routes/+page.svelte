<script lang="ts">
  // Removed onMount and client-side wooCommerceService import
  import type { Product } from '$lib/services/woocommerce'; // Keep type import
  import FAQItem from '$lib/components/FAQItem.svelte';
  import Testimonial from '$lib/components/Testimonial.svelte';
  import PlanCard from '$lib/components/PlanCard.svelte';
  import Typography from '$lib/components/Typography.svelte';
  import SectionHeading from '$lib/components/SectionHeading.svelte';
  import { typography } from '$lib/styles/typography';
  import { trackEvent, MetaPixelEvent } from '$lib/services/meta-pixel';
  import { onMount } from 'svelte'; // Keep onMount for Meta Pixel tracking if needed

  // Define the expected data structure from the server load function
  interface PageData {
    products: Product[];
    apiConfigured: boolean;
    apiError?: string | null;
    error?: string | null; // General error from load function
  }

  // Get data from server load function
  let { data } = $props<{ data: PageData }>();

  // Ensure data is properly initialized and set up state
  const safeData = {
    products: Array.isArray(data?.products) ? data.products : [],
    apiConfigured: data?.apiConfigured ?? false,
    apiError: data?.apiError,
    error: data?.error
  };

  let products: Product[] = $state(safeData.products);
  let apiConfigured = $state(safeData.apiConfigured);
  let apiError = $state(safeData.apiError);
  let error = $state(safeData.error); // General load error
  let isLoading = $state(false); // Data is loaded server-side, so no initial client loading state

  // Track ViewContent event when the component mounts (can keep this)
  onMount(() => {
    trackEvent(MetaPixelEvent.VIEW_CONTENT, {
      content_type: 'home',
      content_name: 'Home Page',
      content_category: 'Homepage'
    });
  });


  // Format price with currency symbol
  function formatPrice(price: string): string {
    if (!price) return '';
    return parseInt(price).toLocaleString() + ' Ks';
  }
</script>

<!-- Hero Section -->
<div class="relative bg-gradient-to-br from-primary/10 to-accent/20 overflow-hidden">
  <div class="absolute inset-0 bg-grid-pattern opacity-10"></div>
  <div class="container mx-auto px-4 py-20 md:py-8">
    <div class="flex flex-col md:flex-row items-center gap-8 md:gap-16">
      <div class="md:w-1/2 z-10">
        <Typography variant="h1" className="mb-6 leading-tight">
          VPN အရှုပ်တော်ပုံထဲက အခုပဲ ရုန်းထွက်လိုက်ပါ
        </Typography>
        <Typography variant="lead" className="mb-10">
          အဘန်းခံရနိုင်ခြေအနည်းဆုံး နည်းပညာနဲ့ IPမန်း VPN
        </Typography>
        <div class="flex flex-wrap gap-4">
          <a
            href="/download"
            class="px-8 py-4 bg-primary text-white font-heading rounded-md hover:bg-primary-dark hover:-translate-y-1 hover:shadow-lg transition-all duration-300 shadow-md text-lg md:text-xl relative overflow-hidden group"
          >
            <span class="relative z-10">DOWNLOAD FREE 1GB</span>
            <span class="absolute inset-0 bg-gradient-to-r from-primary-dark to-primary scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
          </a>
          <a
            href="/plans"
            class="px-8 py-4 border-2 border-primary text-primary font-heading rounded-md hover:bg-primary/10 hover:-translate-y-1 hover:shadow-md transition-all duration-300 text-lg md:text-xl relative overflow-hidden group"
          >
            <span class="relative z-10">အခုဝယ်မည်</span>
            <span class="absolute inset-0 bg-primary/5 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
          </a>
        </div>
      </div>
      <div class="md:w-1/2 relative z-10">
        <img
          src="/images/HeroImg.png"
          alt="IPman VPN Security"
          class="w-full h-auto rounded-xl relative z-10 transform hover:scale-105 transition-transform duration-500"
        />
      </div>
    </div>
  </div>
</div>

<!-- Why IPman is the best VPN Section -->
<section class="py-16 bg-white">
  <div class="container mx-auto px-4">
    <SectionHeading
      title="ဘာကြောင့် IP မန်းက လက်ရှိအကောင်းဆုံး VPN ဖြစ်နေတာလဲ?"
      centered={true}
    />

    <div class="max-w-4xl mx-auto space-y-8">
      <!-- Comparison 1 -->
      <div class="flex flex-col md:flex-row gap-6 items-start">
        <div class="bg-red-50 p-6 rounded-xl md:w-1/2">
          <div class="flex items-start gap-3">
            <span class="text-red-500 text-2xl font-bold">❌</span>
            <p class="text-secondary font-body">အခြား VPN တွေ နာရီနဲ့အမျှ GFW (Great firewall) က ဘန်းတာ ခံနေရတယ်</p>
          </div>
        </div>

        <div class="bg-green-50 p-6 rounded-xl md:w-1/2">
          <div class="flex items-start gap-3">
            <span class="text-green-500 text-2xl font-bold">✅</span>
            <p class="text-secondary font-body">IPမန်း က အဘန်းခံနိုင်ခြေ အနည်းဆုံးဖြစ်မဲ့ အဆင့်မြင့် နည်းပညာတွေ အသုံးပြုထားတယ်။</p>
          </div>
        </div>
      </div>

      <!-- Comparison 2 -->
      <div class="flex flex-col md:flex-row gap-6 items-start">
        <div class="bg-red-50 p-6 rounded-xl md:w-1/2">
          <div class="flex items-start gap-3">
            <span class="text-red-500 text-2xl font-bold">❌</span>
            <p class="text-secondary font-body">အခြား VPN တွေ လတိုင်းသက်တမ်းတိုး လစဉ်ကြေးပေးနေရတယ်</p>
          </div>
        </div>

        <div class="bg-green-50 p-6 rounded-xl md:w-1/2">
          <div class="flex items-start gap-3">
            <span class="text-green-500 text-2xl font-bold">✅</span>
            <p class="text-secondary font-body">IPမန်း GB က မသုံးဖြစ်လည်း ဘယ်တော့မှ Expired မဖြစ်ဘူး သက်တမ်းအကန့်သတ်/ Device အရေအတွက် အကန့်သတ်မရှိ စိတ်ကြိုက်သုံးလိုရတယ်။</p>
          </div>
        </div>
      </div>

      <!-- Comparison 3 -->
      <div class="flex flex-col md:flex-row gap-6 items-start">
        <div class="bg-red-50 p-6 rounded-xl md:w-1/2">
          <div class="flex items-start gap-3">
            <span class="text-red-500 text-2xl font-bold">❌</span>
            <p class="text-secondary font-body">အခြား VPN တွေ ဝယ်တိုင်း setting ချိန်နေရတယ် တပ်ဆင်နည်းတွေ ရှုပ်လွန်းလို အဆင်မပြေဘူး</p>
          </div>
        </div>

        <div class="bg-green-50 p-6 rounded-xl md:w-1/2">
          <div class="flex items-start gap-3">
            <span class="text-green-500 text-2xl font-bold">✅</span>
            <p class="text-secondary font-body">IPမန်း က ဝယ်ယူသူတိုင်းကို ခလုတ်၁ချက်နှိပ်ရုံနဲ့ တပ်ဆင်ပေးမဲ့ အလိုအလျောက်စနစ်ပါတဲ့ ကိုယ်ပိုင် VPN Profile တစ်ခုစီ ပေးတယ်။</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Available Packages Section -->
<section class="py-16 bg-accent/30">
  <div class="container mx-auto px-4">
    <SectionHeading
      title="💎 ဘယ်လို Plan/ Package တွေ ဝယ်ယူလိုရလဲ?"
      centered={true}
    />

    {#if !apiConfigured}
      <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-8" role="alert">
        <strong class="font-bold">API Not Configured!</strong>
        <span class="block sm:inline">Please set up your WooCommerce API credentials in the .env file.</span>
      </div>
    {:else if apiError}
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-8" role="alert">
        <strong class="font-bold">API Error!</strong>
        <span class="block sm:inline">{apiError}</span>
      </div>
    {:else if error}
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-8" role="alert">
        <strong class="font-bold">Error!</strong>
        <span class="block sm:inline">{error}</span>
      </div>
    {:else if products.length === 0}
      <div class="text-center py-12">
        <p class="text-xl text-secondary/70">No plans found or available.</p>
      </div>
    {:else}
      <div class="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
        {#each products as product}
          {#if product.id === 4676 || product.id === 4677 || product.name.toLowerCase().includes('basic') || product.name.toLowerCase().includes('golden')}
            <PlanCard
              product={product}
              isGolden={product.name.toLowerCase().includes('golden') || product.id === 4677}
            />
          {/if}
        {/each}
      </div>

      <!-- Comparison Note -->
      <div class="mt-12 text-center max-w-3xl mx-auto bg-gradient-to-r from-amber-50 to-primary/5 p-8 rounded-xl shadow-md border border-amber-100">
        <Typography variant="p" className="text-lg">
          <span class="font-bold text-amber-600">Golden Plan</span> သည် ပိုမိုကောင်းမွန်သော လိုင်းဆွဲအား၊ ပိုမိုလုံခြုံသော နည်းပညာနှင့် server နေရာ ၄ ခုပါဝင်သောကြောင့် ပိုမိုကောင်းမွန်ပါသည်။ နေ့စဉ်သုံးစွဲမှုအတွက် <span class="font-bold text-primary">Basic Plan</span> သည်လည်း လုံလောက်ပါသည်။
        </Typography>
      </div>
    {/if}
  </div>
</section>

<!-- Features in Every Plan Section -->
<section class="py-16 bg-white">
  <div class="container mx-auto px-4">
    <SectionHeading
      title="5,800 ကျပ်ကနေစတင်ပြီး Plan တိုင်းမှာ..."
      centered={true}
    />

    <div class="max-w-4xl mx-auto space-y-8">
      <!-- Feature 1 -->
      <div class="flex items-start gap-4">
        <div class="bg-primary/10 p-3 rounded-full w-12 h-12 flex items-center justify-center shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div>
          <Typography variant="h3" className="mb-2">Personal VPN Profile (ကိုယ်ပိုင် Profile)</Typography>
          <Typography variant="p">လက်ကျန် ဒေတာ GB အလွယ်တကူစစ်ခြင်း၊ GB တိုးခလုတ်ပါဝင်ခြင်း၊ Automatic system ချိတ်ဆက်ထားခြင်း</Typography>
        </div>
      </div>

      <!-- Feature 2 -->
      <div class="flex items-start gap-4">
        <div class="bg-primary/10 p-3 rounded-full w-12 h-12 flex items-center justify-center shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
        </div>
        <div>
          <Typography variant="h3" className="mb-2">Video tutorials</Typography>
          <Typography variant="p">တပ်ဆင်ဝယ်ယူနည်း တစ်ဆင့်စီ ရှင်းလင်းစွာ လုပ်ဆောင်ပြထားသော ဗီဒီယိုများ ပါဝင်ခြင်း</Typography>
        </div>
      </div>

      <!-- Feature 3 -->
      <div class="flex items-start gap-4">
        <div class="bg-primary/10 p-3 rounded-full w-12 h-12 flex items-center justify-center shrink-0">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
          </svg>
        </div>
        <div>
          <Typography variant="h3" className="mb-2">9AM-9PM support</Typography>
          <Typography variant="p">Telegram channel, Messenger တိုမှာ မိမိသိလိုသမျှ မေးမြန်းထားနိုင်ပါတယ်။ (အစဉ်လိုက်ဖြေဆိုပေးတာမို မေးလိုသမျှ အကုန်မေးထားဖို အကြံပြုလိုပါတယ်။)</Typography>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Free Gift Section -->
<section class="py-20 bg-gradient-to-br from-primary/20 to-accent relative overflow-hidden">
  <div class="absolute top-0 left-0 w-full h-full bg-pattern opacity-5"></div>
  <div class="container mx-auto px-4 text-center relative z-10">
    <div class="max-w-3xl mx-auto bg-white p-10 rounded-xl shadow-xl transform hover:scale-105 transition-all duration-500">
      <div class="text-6xl mb-6 animate-bounce">🎁</div>
      <h2 class="text-3xl md:text-4xl font-heading font-bold text-secondary mb-8">
        IPမန်း App ကို Download ပြီး
        <br>1GB Free Gift ရယူလိုက်ပါ။
      </h2>

      <a
        href="/download"
        class="inline-block px-10 py-4 bg-gradient-to-r from-primary to-primary/90 text-white font-heading rounded-md hover:shadow-lg transition-all duration-300 shadow-md text-xl transform hover:-translate-y-1 relative overflow-hidden group"
      >
        <span class="relative z-10">DOWNLOAD FREE 1GB</span>
        <span class="absolute inset-0 bg-gradient-to-r from-primary-dark to-primary scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
      </a>
    </div>
  </div>
</section>

<!-- How to Use Section -->
<section class="py-16 bg-white">
  <div class="container mx-auto px-4">
    <SectionHeading
      title="ဘယ်လိုအသုံးပြုရမလဲ?"
      centered={true}
    />

    <div class="max-w-4xl mx-auto">
      <!-- Steps -->
      <div class="grid md:grid-cols-3 gap-8">
        <!-- Step 1 -->
        <div class="bg-gradient-to-br from-accent/30 to-white p-8 rounded-xl text-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
          <div class="w-20 h-20 bg-gradient-to-r from-primary to-primary/80 text-white rounded-full flex items-center justify-center text-3xl font-bold mx-auto mb-6 shadow-md">1</div>
          <Typography variant="h3" className="mb-4">Download</Typography>
          <Typography variant="p" className="text-lg">IPမန်း VPN Application ကို Download လုပ်မယ်။</Typography>
        </div>

        <!-- Step 2 -->
        <div class="bg-gradient-to-br from-accent/30 to-white p-8 rounded-xl text-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
          <div class="w-20 h-20 bg-gradient-to-r from-primary to-primary/80 text-white rounded-full flex items-center justify-center text-3xl font-bold mx-auto mb-6 shadow-md">2</div>
          <Typography variant="h3" className="mb-4">Install</Typography>
          <Typography variant="p" className="text-lg">ခလုတ်တစ်ချက်နှိပ်ပြီး VPN တပ်ဆင်မယ်။</Typography>
        </div>

        <!-- Step 3 -->
        <div class="bg-gradient-to-br from-accent/30 to-white p-8 rounded-xl text-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
          <div class="w-20 h-20 bg-gradient-to-r from-primary to-primary/80 text-white rounded-full flex items-center justify-center text-3xl font-bold mx-auto mb-6 shadow-md">3</div>
          <Typography variant="h3" className="mb-4">Connect</Typography>
          <Typography variant="p" className="text-lg">ခလုတ်နှိပ်ပြီး အသုံးပြုနိုင်ပြီ။</Typography>
        </div>
      </div>

      <!-- iOS Note -->
      <div class="mt-16 bg-gradient-to-r from-blue-50 to-accent/10 p-8 rounded-xl shadow-md border border-blue-100">
        <div class="flex items-center gap-4 mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <Typography variant="h3">IPhone/IPad အသုံးပြုသူများ</Typography>
        </div>
        <Typography variant="p" className="pl-14">
          IOS အသုံးပြုသူများအတွက် IPမန်း VPN အစား V2BoX, Hiddify အစရှိတဲ့ Client App များထဲမှ တစ်ခု ကြိုက်နှစ်သက်ရာ တစ်ခုရွေးချယ်ပြီး အသုံးပြုနိုင်မှာဖြစ်ပါတယ်။ တပ်ဆင်နည်း အတူတူပဲဖြစ်ပါတယ်။
        </Typography>
      </div>
    </div>
  </div>
</section>

<!-- Testimonials Section -->
<section class="py-16 bg-accent/30">
  <div class="container mx-auto px-4">
    <SectionHeading
      title="IP မန်း VPN သုံးစွဲသူများရဲ့ စကားသံ"
      centered={true}
    />

    <div class="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
      <!-- Testimonial 1 -->
      <Testimonial name="Aung Aung Htike" rating={5}>
        <p>"ခုချိတ်ခုရ၊ သုံးသလောက်ပဲပေး၊ 24/7 လူနဲ့ပြောစရာမလိုဘဲ ဝယ်နိုင်တာကြောင့် သဘောကျပါတယ်ဗျ"</p>
      </Testimonial>

      <!-- Testimonial 2 -->
      <Testimonial name="Min Thu" rating={5}>
        <p>"လက်တလော ဝယ်သုံးတာတွေအဆင်မပြေဘူး ခုမှဘဲ အဆင်ပြေသွားတယ်.."</p>
      </Testimonial>

      <!-- Testimonial 3 -->
      <Testimonial name="Kyaw Soe Win" rating={5}>
        <p>"လိုင်းကတော့ တော်တော်မြန်တယ်ဗျာ သုံးလိုအဆင်ပြေတယ်"</p>
      </Testimonial>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-16 bg-white">
  <div class="container mx-auto px-4">
    <SectionHeading
      title="FAQ (မေးလေ့ရှိသော မေးခွန်းများ)"
      centered={true}
    />

    <div class="max-w-4xl mx-auto space-y-6">
      <!-- FAQ Item 1 -->
      <FAQItem question="အဆင်မပြေရင် ဘယ်လိုတာဝန်ယူမလဲ?">
        <p>တစ်စုံတစ်ရာ အဆင်မပြေခဲ့ရင် 100% Fully Refund (ငွေအပြည့်ပြန်အမ်းစနစ်) နဲ့ အာမခံ ရောင်းချပေးပါတယ်။</p>
      </FAQItem>

      <!-- FAQ Item 2 -->
      <FAQItem question="GB ဒေတာလက်ကျန် ဘယ်လိုစစ်ရမလဲ?">
        <p>Telegram bot က ပေးထားတဲ့ Personal VPN Profile ထဲမှာ လက်ကျန် GB ပြထားပေးပါတယ်။ အလွယ်တကူ GB တိုးဖို ခလုတ်လည်း ထည့်ပေးထားပါတယ်။</p>
      </FAQItem>

      <!-- FAQ Item 3 -->
      <FAQItem question="ဝယ်ပြီးသား vpn ကို အခြားဖုန်းထဲ/PC ထဲဘယ်လိုထပ်ထည့်ရမလဲ?">
        <p>မိမိဝယ်ယူထားတဲ့ Personal VPN Profile ထဲမှာ "Share မည်" ခလုတ်လေးနဲ့ တပ်ဆင်လိုတဲ့ Device ကို လှမ်းပို့ရုံပါပဲ။</p>
      </FAQItem>

      <!-- FAQ Item 4 -->
      <FAQItem question="ဝယ်ယူတပ်ဆင်နည်းအသေးစိတ် ဘယ်မှာကြည့်ရမလဲ?">
        <div>
          <p>ဝယ်ယူတပ်ဆင်နည်း အသေးစိတ်ကို <a href="https://vimeo.com/1081818264" target="_blank" class="text-primary hover:underline font-medium">ဒီမှာကြည့်လိုရပါတယ်</a>။</p>
          <p class="mt-2">မိမိနှစ်သက်ရာ Client app (IPမန်း, V2Box, Streisand, Hiddify) စသည် တစ်ခုချင်းစီရဲ့ အသုံးပြုနည်းကိုတော့ VPN profile ထဲမှာ သက်ဆိုင်ရာ ထည့်ပေးထားပါတယ်။</p>
        </div>
      </FAQItem>

      <!-- FAQ Item 5 -->
      <FAQItem question="လကုန်တိုင်းသက်တမ်းတိုး ပြန်ဝယ်ရမှာလား?">
        <p>GB မကုန်မချင်း ရာသက်ပန် သုံးလိုရနေမှာပါ။ သက်တမ်း မကန့်သတ်ထားသလို Device အရေအတွက်ကိုလည်း စိတ်ကြိုက်ဝေမျှ သုံးနိုင်ပါတယ်။</p>
      </FAQItem>

      <!-- FAQ Item 6 -->
      <FAQItem question="Golden Plan ဝယ်ဖိုလိုလား?">
        <div>
          <p>ပိုမိုရိုးရှင်းတဲ့ VPN Profile/ ပိုကောင်းမွန်တဲ့ လိုင်းဆွဲအား + Premium Server (၄)ခု ပါဝင်တဲ့ <span class="text-amber-600 font-semibold">Golden</span> ကတော့ IP မန်း ရဲ့ Recommendation ဖြစ်ပါတယ်။</p>
          <p class="mt-2">နေ့စဉ်အသုံးပြုနေကျ Facebook/ Messenger လောက် သုံးစွဲသူဆိုရင် <span class="text-primary font-semibold">Basic Plan</span> နဲ့ အဆင်ပြေပါတယ်။</p>
        </div>
      </FAQItem>

      <!-- FAQ Item 7 -->
      <FAQItem question="နိုင်ငံခြားမှာ သုံးလိုရလား? (ဥပမာ- China)">
        <p>လက်ရှိ တရုတ်နိုင်ငံမှာ IPမန်း သုံးစွဲ အဆင်ပြေနေသူတွေရှိပါတယ်။ စမ်းကြည့်နိုင်ပါတယ်။</p>
      </FAQItem>
    </div>
  </div>
</section>

<!-- Final CTA Section -->
<section class="py-24 bg-gradient-to-br from-primary to-secondary relative overflow-hidden">
  <div class="absolute inset-0 bg-pattern opacity-10"></div>
  <div class="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full blur-3xl -mr-32 -mt-32"></div>
  <div class="absolute bottom-0 left-0 w-64 h-64 bg-white/10 rounded-full blur-3xl -ml-32 -mb-32"></div>

  <div class="container mx-auto px-4 text-center relative z-10">
    <div class="max-w-4xl mx-auto">
      <Typography variant="h2" className="text-white mb-8 leading-tight">
        အခုပဲ IPမန်း VPN ကို စတင်သုံးစွဲကြည့်ပါ
      </Typography>
      <Typography variant="lead" className="text-white/90 mb-12">
        အဘန်းခံရနိုင်ခြေအနည်းဆုံး နည်းပညာနဲ့ အသုံးပြုရလွယ်ကူတဲ့ VPN ကို ရယူလိုက်ပါ။
      </Typography>
      <div class="flex flex-wrap justify-center gap-6">
        <a
          href="/download"
          class="px-10 py-4 bg-white text-primary font-heading rounded-md hover:bg-opacity-90 transition-all duration-300 shadow-lg text-xl transform hover:-translate-y-1 relative overflow-hidden group"
        >
          <span class="relative z-10">DOWNLOAD FREE 1GB</span>
          <span class="absolute inset-0 bg-white/80 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
        </a>
        <a
          href="/plans"
          class="px-10 py-4 border-2 border-white text-white font-heading rounded-md hover:bg-white/20 transition-all duration-300 text-xl transform hover:-translate-y-1 relative overflow-hidden group"
        >
          <span class="relative z-10">အခုဝယ်မည်</span>
          <span class="absolute inset-0 bg-white/20 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
        </a>
      </div>
    </div>
  </div>
</section>
