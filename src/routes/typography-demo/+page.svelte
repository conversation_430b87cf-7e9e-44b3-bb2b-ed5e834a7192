<script lang="ts">
  import Typography from '$lib/components/Typography.svelte';
  import { typography } from '$lib/styles/typography';
</script>

<div class="container mx-auto px-4 py-12">
  <Typography variant="h1" className="mb-8">Typography System</Typography>
  
  <div class="mb-12">
    <Typography variant="h2" className="mb-4">Headings</Typography>
    <div class="space-y-4 mb-8">
      <Typography variant="h1">Heading 1</Typography>
      <Typography variant="h2">Heading 2</Typography>
      <Typography variant="h3">Heading 3</Typography>
      <Typography variant="h4">Heading 4</Typography>
      <Typography variant="h5">Heading 5</Typography>
      <Typography variant="h6">Heading 6</Typography>
    </div>
    
    <div class="bg-gray-100 p-4 rounded-md">
      <Typography variant="small" className="mb-2">Example usage:</Typography>
      <pre class="text-sm bg-gray-800 text-white p-4 rounded-md overflow-x-auto">
{`<Typography variant="h1">Heading 1</Typography>
<Typography variant="h2">Heading 2</Typography>
<h2 class="${typography.heading.h2}">Heading 2 (using class directly)</h2>`}
      </pre>
    </div>
  </div>
  
  <div class="mb-12">
    <Typography variant="h2" className="mb-4">Paragraphs</Typography>
    <div class="space-y-4 mb-8">
      <Typography variant="lead">Lead paragraph - Use this for introductory text or important paragraphs that need to stand out.</Typography>
      <Typography variant="p">Base paragraph - This is the standard paragraph style used for most content throughout the site.</Typography>
      <Typography variant="small">Small paragraph - Use this for less important information, captions, or secondary content.</Typography>
      <Typography variant="tiny">Tiny text - Use this for footnotes, copyright information, or other very small text.</Typography>
    </div>
    
    <div class="bg-gray-100 p-4 rounded-md">
      <Typography variant="small" className="mb-2">Example usage:</Typography>
      <pre class="text-sm bg-gray-800 text-white p-4 rounded-md overflow-x-auto">
{`<Typography variant="lead">Lead paragraph</Typography>
<Typography variant="p">Base paragraph</Typography>
<p class="${typography.paragraph.base}">Base paragraph (using class directly)</p>`}
      </pre>
    </div>
  </div>
  
  <div class="mb-12">
    <Typography variant="h2" className="mb-4">Special Text Styles</Typography>
    <div class="space-y-4 mb-8">
      <p class={typography.special.price}>$19.99 - Price</p>
      <p class={typography.special.priceLarge}>$19.99 - Large Price</p>
      <p><span class={typography.special.price}>$19.99</span> <span class={typography.special.priceStrike}>$29.99</span> - Price with strike</p>
      <p class={typography.special.label}>Form Label</p>
      <a href="#" class={typography.special.link}>Link Style</a>
      <p class={typography.special.error}>Error Message</p>
      <p class={typography.special.success}>Success Message</p>
    </div>
    
    <div class="bg-gray-100 p-4 rounded-md">
      <Typography variant="small" className="mb-2">Example usage:</Typography>
      <pre class="text-sm bg-gray-800 text-white p-4 rounded-md overflow-x-auto">
{`<p class={typography.special.price}>$19.99</p>
<span class={typography.special.error}>Error message</span>`}
      </pre>
    </div>
  </div>
  
  <div class="mb-12">
    <Typography variant="h2" className="mb-4">Custom Usage</Typography>
    <div class="space-y-4 mb-8">
      <Typography variant="h3" color="text-primary">Custom Color Heading</Typography>
      <Typography variant="p" className="italic">Custom Class Paragraph (italic)</Typography>
      <Typography variant="h4" as="div">Heading 4 Style as Div Element</Typography>
    </div>
    
    <div class="bg-gray-100 p-4 rounded-md">
      <Typography variant="small" className="mb-2">Example usage:</Typography>
      <pre class="text-sm bg-gray-800 text-white p-4 rounded-md overflow-x-auto">
{`<Typography variant="h3" color="text-primary">Custom Color Heading</Typography>
<Typography variant="p" className="italic">Custom Class Paragraph</Typography>
<Typography variant="h4" as="div">Heading 4 Style as Div</Typography>`}
      </pre>
    </div>
  </div>
  
  <div class="mt-12 p-6 border border-gray-200 rounded-lg">
    <Typography variant="h3" className="mb-4">How to Update Typography Globally</Typography>
    <Typography variant="p" className="mb-4">
      To change typography styles across the entire site, simply edit the <code class="bg-gray-100 px-1 py-0.5 rounded">src/lib/styles/typography.ts</code> file.
      All components using these styles will automatically update.
    </Typography>
    <div class="bg-gray-100 p-4 rounded-md">
      <pre class="text-sm bg-gray-800 text-white p-4 rounded-md overflow-x-auto">
{`// Example: Change h1 size in typography.ts
export const headingStyles = {
  h1: 'text-5xl md:text-7xl font-heading font-bold text-secondary', // Updated size
  // ...other heading styles
};`}
      </pre>
    </div>
  </div>
</div>
