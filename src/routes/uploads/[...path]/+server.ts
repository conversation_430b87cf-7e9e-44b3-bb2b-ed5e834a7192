import { error } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { promises as fs } from 'fs';
import path from 'path';
import { env } from '$env/dynamic/private';

// Get upload directory from environment variable
// The base upload directory (without payment-proofs subdirectory)
const UPLOAD_DIR = env.UPLOAD_DIR ? path.dirname(env.UPLOAD_DIR) : '/app/static/uploads';

export const GET: RequestHandler = async ({ params }) => {
  try {
    const filePath = params.path;

    if (!filePath) {
      throw error(404, 'File not found');
    }

    // Security: Prevent directory traversal attacks
    if (filePath.includes('..') || filePath.includes('\\')) {
      throw error(403, 'Access denied');
    }

    // Construct the full file path
    const fullPath = path.join(UPLOAD_DIR, filePath);
    
    // Check if file exists
    try {
      await fs.access(fullPath);
    } catch {
      throw error(404, 'File not found');
    }

    // Read the file
    const fileBuffer = await fs.readFile(fullPath);
    
    // Determine content type based on file extension
    const ext = path.extname(filePath).toLowerCase();
    let contentType = 'application/octet-stream';
    
    switch (ext) {
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
      case '.svg':
        contentType = 'image/svg+xml';
        break;
      default:
        contentType = 'application/octet-stream';
    }

    return new Response(fileBuffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
        'Content-Length': fileBuffer.length.toString(),
      },
    });
  } catch (err) {
    console.error('Error serving uploaded file:', err);
    if (err instanceof Error && 'status' in err) {
      throw err;
    }
    throw error(500, 'Internal server error');
  }
};
