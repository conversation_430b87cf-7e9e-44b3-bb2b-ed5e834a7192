<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';
  import { Progress } from '$lib/components/ui/progress';
  import QRCode from 'qrcode';
  import BandwidthUpgradeModal from '$lib/components/BandwidthUpgradeModal.svelte';
  import { getBooleanUrlParam, getNumericUrlParam, getUrlParam, setUrlParams } from '$lib/utils/url';

  // Platform detection
  let currentPlatform = $state('unknown');
  let showVideoTutorial = $state(false);
  let selectedPlatform = $state<string | null>(null);
  let selectedApp = $state<string>('primary'); // 'primary' or 'alternative'

  // VPN app information
  const vpnApps = {
    primary: {
      name: {
        android: 'IPman',
        ios: 'Hiddify',
        windows: 'IPman',
        macos: 'IPman',
      },
      downloadUrl: {
        android: 'https://intarnad.com/IPman.apk',
        ios: 'https://apps.apple.com/us/app/hiddify-proxy-vpn/id6596777532',
        windows: 'https://intarnad.com/IPman.exe',
        macos: 'https://intarnad.com/IPman.dmg',
      },
      deeplinkFormat: {
        android: (url: string) => `ipman://import/${url}`,
        ios: (url: string) => `hiddify://import/${url}`,
        windows: (url: string) => `ipman://import/${url}`,
        macos: (url: string) => `hiddify://import/${url}`,
      },
      tutorialUrl: {
        android: 'https://player.vimeo.com/video/1081820034', // IPman
        ios: 'https://player.vimeo.com/video/1081817047', // Placeholder
        windows: 'https://player.vimeo.com/video/1081820034', // Placeholder
        macos: 'https://player.vimeo.com/video/1081820034', // Placeholder
      }
    },
    alternative: {
      name: {
        android: 'V2BoX',
        ios: 'V2BoX',
        windows: 'Hiddify',
        macos: 'Hiddify'
      },
      downloadUrl: {
        android: 'https://play.google.com/store/apps/details?id=dev.hexasoftware.v2box&hl=en',
        ios: 'https://apps.apple.com/us/app/v2box-v2ray-client/id6446814690',
        windows: 'https://apps.microsoft.com/detail/9pdfnl3qv2s5?hl=en-US&gl=US',
        macos: 'https://apps.apple.com/us/app/hiddify-proxy-vpn/id6596777532',
      },
      deeplinkFormat: {
        android: (url: string) => `v2box://install-config?url=${url}`,
        ios: (url: string) => `v2box://install-sub?url=${url}&name=${userName}`,
        windows: (url: string) => `hiddify://import/${url}`,
        macos: (url: string) => `hiddify://import/${url}`,
      },
      tutorialUrl: {
        android: 'https://player.vimeo.com/video/1081820267', // V2box
        ios: 'https://player.vimeo.com/video/1081820267', // V2box
        windows: 'https://player.vimeo.com/video/1081817978', // Hiddify
        macos: 'https://player.vimeo.com/video/1081817978', // Hiddify
      }
    }
  };

  // Types for the API response
  interface ProfileData {
    admin_message_html: string;
    admin_message_url: string;
    brand_icon_url: string;
    brand_title: string;
    doh: string;
    lang: string;
    profile_remaining_days: number;
    profile_reset_days: number;
    profile_title: string;
    profile_url: string;
    profile_usage_current: number;
    profile_usage_total: number;
    speedtest_enable: boolean;
    telegram_bot_url: string;
    telegram_id: string | null;
    telegram_proxy_enable: boolean;
  }

  // New API response interface
  interface NewProfileData {
    proxies: {
      shadowsocks: {
        password: string;
        method: string;
      }
    };
    status: string;
    data_limit: number;
    data_limit_reset_strategy: string;
    expire: string | null;
    used_traffic: number;
    on_hold_expire_duration: string | null;
    on_hold_timeout: string | null;
    created_at: string;
    admin_username: string | null;
    note: string;
    custom_subscription_path: string;
    custom_uuid: string;
    subscription_path: string | null;
    subscription_token: string | null;
    sub_updated_at: string | null;
    sub_last_user_agent: string | null;
    online_at: string | null;
    next_plan: string | null;
    username: string;
    lifetime_used_traffic: number;
    links: string[];
    subscription_url: string;
  }

  // State
  let loading = $state(true);
  let error = $state<string | null>(null);
  let profileData = $state<ProfileData | null>(null);
  let newProfileData = $state<NewProfileData | null>(null);
  let orderNumber = $state<string | null>(null);
  let userName = $state<string | null>(null);
  let usagePercentage = $state(0);
  let qrCodeDataURL = $state('');
  let copySuccess = $state(false);
  let copyTimeout: ReturnType<typeof setTimeout> | null = null;
  let subscriptionUrl = $state(''); // Store the URL from query parameter

  // Checkout modal state
  let showCheckoutModal = $state(false);
  let checkoutOrderId = $state<number | null>(null);
  let checkoutOrderNumber = $state<string>('');
  let checkoutSuccess = $state(false);

  // No longer using local storage for modal state

  // Function to update URL parameters
  function updateState() {
    if (browser) {
      try {
        // Update URL parameters for modal states
        setUrlParams({
          'modal': showCheckoutModal ? 'bandwidth' : null,
          'success': checkoutSuccess ? 'true' : null,
          'order_id': checkoutOrderId ? checkoutOrderId.toString() : null,
          'order_number': checkoutOrderNumber || null
        });
      } catch (error) {
        console.error('Error updating URL parameters:', error);
      }
    }
  }

  // Handle checkout success
  function handleCheckoutSuccess(event: { orderId: number; orderNumber: string }) {
    checkoutOrderId = event.orderId;
    checkoutOrderNumber = event.orderNumber;
    checkoutSuccess = true;
    updateState();
  }

  // Close checkout modal
  function closeCheckoutModal() {
    showCheckoutModal = false;

    // If we're closing after a successful checkout, clear the state
    if (checkoutSuccess) {
      checkoutSuccess = false;
      checkoutOrderId = null;
      checkoutOrderNumber = '';
    }

    updateState();
  }

  // Copy profile URL to clipboard
  function copyProfileURL() {
    if (!subscriptionUrl || !browser) return;

    // Create a fallback method using a temporary textarea element
    const fallbackCopyTextToClipboard = (text: string) => {
      const textArea = document.createElement('textarea');
      textArea.value = text;

      // Make the textarea out of viewport
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);

      // Select and copy the text
      textArea.focus();
      textArea.select();

      let success = false;
      try {
        // Using a different approach instead of the deprecated execCommand
        document.getSelection()?.removeAllRanges();
        const range = document.createRange();
        range.selectNode(textArea);
        document.getSelection()?.addRange(range);
        success = true;
        try {
          success = document.execCommand('copy');
        } catch (err) {
          console.error('Fallback: Oops, unable to copy', err);
          success = false;
        }
        document.getSelection()?.removeAllRanges();
      } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
      }

      // Clean up
      document.body.removeChild(textArea);
      return success;
    };

    // Try to use the modern clipboard API first
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(subscriptionUrl)
        .then(() => {
          copySuccess = true;
          if (copyTimeout) clearTimeout(copyTimeout);
          copyTimeout = setTimeout(() => copySuccess = false, 2000);
        })
        .catch(err => {
          console.error('Failed to copy with Clipboard API:', err);
          // Try the fallback method
          const success = fallbackCopyTextToClipboard(subscriptionUrl);
          if (success) {
            copySuccess = true;
            if (copyTimeout) clearTimeout(copyTimeout);
            copyTimeout = setTimeout(() => copySuccess = false, 2000);
          } else {
            alert('Failed to copy URL. Please try again or copy it manually.');
          }
        });
    } else {
      // Clipboard API not available, use fallback
      const success = fallbackCopyTextToClipboard(subscriptionUrl);
      if (success) {
        copySuccess = true;
        if (copyTimeout) clearTimeout(copyTimeout);
        copyTimeout = setTimeout(() => copySuccess = false, 2000);
      } else {
        alert('Failed to copy URL. Please try again or copy it manually.');
      }
    }
  }

  // Platform detection function
  function detectPlatform() {
    if (!browser) return 'unknown';

    const userAgent = navigator.userAgent.toLowerCase();

    if (/(android)/i.test(userAgent)) {
      return 'android';
    }
    if (/(iphone|ipad|ipod)/i.test(userAgent)) {
      return 'ios';
    }
    if (/(mac)/i.test(userAgent) && !/(iphone|ipad|ipod)/i.test(userAgent)) {
      return 'macos';
    }
    if (/(win)/i.test(userAgent)) {
      return 'windows';
    }

    return 'unknown';
  }

  // Handle app download
  function downloadApp(platform: string, appType: string = 'primary') {
    if (!browser) return;

    const app = vpnApps[appType as keyof typeof vpnApps];
    if (!app) return;

    const downloadUrl = app.downloadUrl[platform as keyof typeof app.downloadUrl];
    if (downloadUrl) {
      window.open(downloadUrl, '_blank');
    }
  }

  // Handle subscription URL deeplink
  function addSubscription(appType: string = 'primary') {
    if (!subscriptionUrl || !browser) return;

    const app = vpnApps[appType as keyof typeof vpnApps];
    if (!app) return;

    const deeplinkFormatFn = app.deeplinkFormat[currentPlatform as keyof typeof app.deeplinkFormat];
    if (!deeplinkFormatFn) return;

    const deeplink = deeplinkFormatFn(subscriptionUrl);


      // For mobile, use deeplink
      window.location.href = deeplink;

  }

  // Show video tutorial
  function showTutorial(platform: string, appType: string = 'primary') {
    selectedPlatform = platform;
    selectedApp = appType;
    showVideoTutorial = true;
  }

  // Close video tutorial
  function closeTutorial() {
    showVideoTutorial = false;
    selectedPlatform = null;
  }

  // Get tutorial video URL based on platform and app type
  function getTutorialUrl(platform: string, appType: string = 'primary'): string {
    const app = vpnApps[appType as keyof typeof vpnApps];
    if (!app) return '';

    return app.tutorialUrl[platform as keyof typeof app.tutorialUrl] || '';
  }



  // Extract API endpoint from URL query parameter
  onMount(async () => {
    // Load state from URL parameters
    if (browser) {
      try {
        // Check for modal state in URL parameters
        const modalParam = getUrlParam('modal');
        const successParam = getBooleanUrlParam('success');
        const orderIdParam = getNumericUrlParam('order_id');
        const orderNumberParam = getUrlParam('order_number');

        // Set modal states based on URL parameters
        showCheckoutModal = modalParam === 'bandwidth';

        if (successParam !== null) {
          checkoutSuccess = successParam;
        }

        if (orderIdParam !== null) {
          checkoutOrderId = orderIdParam;
        }

        if (orderNumberParam !== null) {
          checkoutOrderNumber = orderNumberParam;
        }
      } catch (err) {
        console.error('Error loading state from URL parameters:', err);
      }
    }

    // Detect platform
    currentPlatform = detectPlatform();
    console.log('Detected platform:', currentPlatform);

    try {
      // Get URL parameters directly from the browser
      const url = browser ? new URL(window.location.href) : null;
      const linkParam = url ? url.searchParams.get('link') : null;

      if (!linkParam) {
        error = 'No link parameter provided';
        loading = false;
        return;
      }

      // Construct the API URL by appending the path
      const targetApiUrl = `${linkParam}/info`;

      // Use our proxy endpoint to avoid CORS issues
      const proxyUrl = `/api/proxy?url=${encodeURIComponent(targetApiUrl)}`;

      console.log('Fetching data via proxy from:', targetApiUrl);

      // Fetch data from the API via our proxy
      const response = await fetch(proxyUrl);

      if (!response.ok) {
        error = `API request failed with status ${response.status}`;
        loading = false;
        return;
      }

      // Parse the response
      newProfileData = await response.json();

      // Extract user name and order number from the new API response
      if (newProfileData) {
        userName = newProfileData.note || 'User';
        orderNumber = newProfileData.username || null;
        
        // Calculate usage percentage - convert bytes to GB
        const usedTrafficGB = newProfileData.used_traffic / 1024 / 1024 / 1024;
        const dataLimitGB = newProfileData.data_limit / 1024 / 1024 / 1024;
        
        if (newProfileData.data_limit > 0) {
          usagePercentage = (newProfileData.used_traffic / newProfileData.data_limit) * 100;
        }
        
        // If the API provides a subscription_url directly, use it
        if (newProfileData.subscription_url) {
          subscriptionUrl = newProfileData.subscription_url;
        } else {
          // Otherwise keep using the linkParam as before
          subscriptionUrl = linkParam;
        }
      }

      // Generate QR code for the subscription URL
      try {
        qrCodeDataURL = await QRCode.toDataURL(subscriptionUrl, {
            width: 200,
            margin: 1,
            color: {
              dark: '#000000',
              light: '#ffffff'
            }
          });
        } catch (qrErr) {
          console.error('Error generating QR code:', qrErr);
        }

      loading = false;
    } catch (err) {
      console.error('Error fetching profile data:', err);
      error = err instanceof Error ? err.message : 'Unknown error occurred';
      loading = false;
    }
  });
</script>

<div class="container mx-auto px-4 py-8">
  <!-- Bandwidth Upgrade Modal -->
  {#if showCheckoutModal}
    <BandwidthUpgradeModal
      subscriptionUrl={subscriptionUrl}
      userName={userName}
      onClose={closeCheckoutModal}
      onSuccess={handleCheckoutSuccess}
      initialOrderId={checkoutOrderId}
      initialOrderNumber={checkoutOrderNumber}
      initialSuccess={checkoutSuccess}
    />
  {/if}
  <div class="max-w-2xl mx-auto">
    <h1 class="text-6xl font-bold text-center mb-8">မင်္ဂလာပါ။</h1>

    {#if loading}
      <div class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    {:else if error}
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <strong class="font-bold">Error!</strong>
        <span class="block sm:inline"> {error}</span>
      </div>
    {:else if newProfileData}
      <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- Header with user info -->
        <div class="bg-gradient-to-r bg-primary to-indigo-700 text-white p-6">
          <h2 class="text-3xl font-bold">{userName || 'User'}</h2>
          {#if orderNumber}
            <p class="text-white/80">Order အမှတ်: {orderNumber}</p>
          {/if}
        </div>

        <!-- Profile details -->
        <div class="p-6 space-y-6">
          <!-- Bandwidth usage -->
          <div>
            <div class="flex justify-between mb-2">
              <span class="font-medium">GB ပမာဏ</span>
              <span class="text-gray-600">
                {(newProfileData.used_traffic / 1024 / 1024 / 1024).toFixed(2)} GB / {(newProfileData.data_limit / 1024 / 1024 / 1024).toFixed(2)} GB
              </span>
            </div>

            <Progress value={usagePercentage} class="h-6" />

            <div class="mt-1 text-right text-sm text-gray-500">
              {usagePercentage.toFixed(1)}% သုံးပြီး
            </div>
          </div>
          <button
              class="place-self-center flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
              onclick={() => {
                showCheckoutModal = true;
                updateState();
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" class=" h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
              GB တိုးမည်
          </button>

          <!-- QR Code and Copy URL Section -->
          <div class="mt-4 p-4 bg-white shadow-md rounded-lg overflow-hidden border border-gray-200">
            <h3 class="text-3xl font-medium mb-3 text-center">QR Code</h3>

            <div class="flex flex-col items-center">
              {#if qrCodeDataURL}
                <div class="mb-3 p-2 bg-white rounded-lg border border-gray-200 inline-block">
                  <img src={qrCodeDataURL} alt="Subscription QR Code" class="w-40 h-40" />
                </div>
              {/if}

              <button
                class="flex items-center px-4 py-2 {copySuccess ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-800'} rounded-lg hover:{copySuccess ? 'bg-green-600' : 'bg-gray-300'} transition-colors"
                onclick={copyProfileURL}
              >
                {#if copySuccess}
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Copy ပြီး!
                {:else}
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                  </svg>
                  Copy Link
                {/if}
              </button>
            </div>
          </div>
          <!-- Remaining days -->
          <!-- <div class="border-t pt-4">
            <div class="flex justify-between">
              <span class="font-medium">Remaining Days</span>
              <span class="text-gray-600">{profileData.profile_remaining_days} days</span>
            </div>
          </div> -->

          <!-- Reset cycle -->
          <!-- <div class="border-t pt-4">
            <div class="flex justify-between">
              <span class="font-medium">Reset Cycle</span>
              <span class="text-gray-600">
                {profileData.profile_reset_days === 9999 ? 'Never' : `${profileData.profile_reset_days} days`}
              </span>
            </div>
          </div> -->
        </div>
      </div>

      <!-- Download and Setup Section -->
      <div class="mt-8 bg-white shadow-md rounded-lg overflow-hidden">
        <div class="bg-gradient-to-r bg-primary to-indigo-700 text-white p-6">
          <h2 class="text-3xl font-bold flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            IPမန်း VPN စတင်အသုံးပြုရန်
          </h2>
          <p class="text-white/80 mt-1">တပ်ဆင်အသုံးပြုရန် အောက်ပါအချက် (၃) ချက်ကို လုပ်ဆောင်ပါ။</p>
        </div>

        <div class="p-6">
          <!-- Platform Tabs -->
          <div class="flex flex-wrap border-b mb-6">
            <button
              class="px-4 py-2 font-medium {currentPlatform === 'android' ? 'text-primary border-b-2 border-primary' : 'text-gray-500 hover:text-gray-700'}"
              onclick={() => currentPlatform = 'android'}
            >
              <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M6 18c0 .55.45 1 1 1h1v3.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V19h2v3.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5V19h1c.55 0 1-.45 1-1V8H6v10zM3.5 8C2.67 8 2 8.67 2 9.5v7c0 .83.67 1.5 1.5 1.5S5 17.33 5 16.5v-7C5 8.67 4.33 8 3.5 8zm17 0c-.83 0-1.5.67-1.5 1.5v7c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5v-7c0-.83-.67-1.5-1.5-1.5zm-4.97-5.84l1.3-1.3c.2-.2.2-.51 0-.71-.2-.2-.51-.2-.71 0l-1.48 1.48C13.85 1.23 12.95 1 12 1c-.96 0-1.86.23-2.66.63L7.85.15c-.2-.2-.51-.2-.71 0-.2.2-.2.51 0 .71l1.31 1.31C6.97 3.26 6 5.01 6 7h12c0-1.99-.97-3.75-2.47-4.84zM10 5H9V4h1v1zm5 0h-1V4h1v1z"/>
                </svg>
                Android
              </div>
            </button>
            <button
              class="px-4 py-2 font-medium {currentPlatform === 'ios' ? 'text-primary border-b-2 border-primary' : 'text-gray-500 hover:text-gray-700'}"
              onclick={() => currentPlatform = 'ios'}
            >
              <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M17.05 20.28c-.98.95-2.05.8-3.08.35-1.09-.46-2.09-.48-3.24 0-1.44.62-2.2.44-3.06-.35C2.79 15.25 3.51 7.59 8.42 7.31c1.33.07 2.25.78 3.13.84.95-.14 2.04-.84 3.3-.84 1.4.03 2.46.56 3.25 1.53-2.92 1.7-2.41 5.73.41 6.85-.7 1.97-1.58 3.97-3.46 5.59zm-3.23-17.4c.65-.95.58-2.28.44-2.88-1.01.11-2.13.8-2.73 1.67-.65.84-.7 1.98-.53 2.74 1.01.05 2.04-.59 2.82-1.53z"/>
                </svg>
                iOS
              </div>
            </button>
            <button
              class="px-4 py-2 font-medium {currentPlatform === 'windows' ? 'text-primary border-b-2 border-primary' : 'text-gray-500 hover:text-gray-700'}"
              onclick={() => currentPlatform = 'windows'}
            >
              <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M3 5.29V7h9V3H5.71c-.56 0-1.09.23-1.47.63L3 5.29zm18-2.29v16c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2v-7h9v-2H3V5c0-.55.23-1.06.62-1.42L6.83 1H19c1.1 0 2 .9 2 2zm-2 0H7v2h12V3z"/>
                </svg>
                Windows
              </div>
            </button>
            <button
              class="px-4 py-2 font-medium {currentPlatform === 'macos' ? 'text-primary border-b-2 border-primary' : 'text-gray-500 hover:text-gray-700'}"
              onclick={() => currentPlatform = 'macos'}
            >
              <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M17.05 20.28c-.98.95-2.05.8-3.08.35-1.09-.46-2.09-.48-3.24 0-1.44.62-2.2.44-3.06-.35C2.79 15.25 3.51 7.59 8.42 7.31c1.33.07 2.25.78 3.13.84.95-.14 2.04-.84 3.3-.84 1.4.03 2.46.56 3.25 1.53-2.92 1.7-2.41 5.73.41 6.85-.7 1.97-1.58 3.97-3.46 5.59zm-3.23-17.4c.65-.95.58-2.28.44-2.88-1.01.11-2.13.8-2.73 1.67-.65.84-.7 1.98-.53 2.74 1.01.05 2.04-.59 2.82-1.53z"/>
                </svg>
                macOS
              </div>
            </button>
          </div>

          <!-- Step 1: Download App -->
          <div class="mb-6 p-4 border rounded-lg bg-gray-50">
            <h3 class="text-3xl font-medium flex items-center">
              <span class="flex items-center justify-center bg-primary text-white rounded-full w-6 h-6 mr-2 text-sm">1</span>
              VPN App Download လုပ်ပါ
            </h3>
            <p class="mt-2 text-gray-600 mb-4">ကြိုက်နှစ်သက်ရာ VPN App အားရွေးချယ် Download လုပ်ပါ-</p>

            <!-- App Selection Tabs -->
            <div class="flex border-b mb-4">
              <button
                class="px-4 py-2 font-medium {selectedApp === 'primary' ? 'text-primary border-b-2 border-primary' : 'text-gray-500 hover:text-gray-700'}"
                onclick={() => selectedApp = 'primary'}
              >
                {vpnApps.primary.name[currentPlatform as keyof typeof vpnApps.primary.name]}
              </button>
              <button
                class="px-4 py-2 font-medium {selectedApp === 'alternative' ? 'text-primary border-b-2 border-primary' : 'text-gray-500 hover:text-gray-700'}"
                onclick={() => selectedApp = 'alternative'}
              >
                {vpnApps.alternative.name[currentPlatform as keyof typeof vpnApps.alternative.name]}
              </button>
            </div>

            <!-- Primary App Card -->
            {#if selectedApp === 'primary'}
              <div class="bg-white border rounded-lg p-4 mb-4">
                <div class="flex items-center mb-3">
                  <div class="bg-primary/10 p-2 rounded-full mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="text-2xl font-medium">{vpnApps.primary.name[currentPlatform as keyof typeof vpnApps.primary.name]}</h4>
                    <p class="text-sm text-gray-500">Primary VPN client</p>
                  </div>
                </div>

                <div class="flex flex-wrap gap-2">
                  <button
                    class="flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                    onclick={() => downloadApp(currentPlatform, 'primary')}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    Download
                  </button>
                </div>
              </div>
            {:else}
              <!-- Alternative App Card -->
              <div class="bg-white border rounded-lg p-4 mb-4">
                <div class="flex items-center mb-3">
                  <div class="bg-primary/10 p-2 rounded-full mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <div>
                    <h4 class=" text-2xl font-medium">{vpnApps.alternative.name[currentPlatform as keyof typeof vpnApps.alternative.name]}</h4>
                    <p class="text-sm text-gray-500">Alternative VPN client</p>
                  </div>
                </div>

                <div class="flex flex-wrap gap-2">
                  <button
                    class="flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                    onclick={() => downloadApp(currentPlatform, 'alternative')}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    Download
                  </button>
                </div>
              </div>
            {/if}
          </div>

          <!-- Step 2: Add Subscription -->
          <div class="mb-6 p-4 border rounded-lg bg-gray-50">
            <h3 class="text-3xl font-medium flex items-center">
              <span class="flex items-center justify-center bg-primary text-white rounded-full w-6 h-6 mr-2 text-sm">2</span>
              လင့်ထည့်မည်
            </h3>
            <p class="mt-2 text-gray-600 mb-4">IPမန်း VPN လင့်အား App ထဲသို့ထည့်ပါ-</p>

            {#if selectedApp === 'primary'}
              <button
                class="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                onclick={() => addSubscription('primary')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                လင့်အား {vpnApps.primary.name[currentPlatform as keyof typeof vpnApps.primary.name] } သို့ထည့်ရန်
              </button>
            {:else}
              <button
                class="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                onclick={() => addSubscription('alternative')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                လင့်အား {vpnApps.alternative.name[currentPlatform as keyof typeof vpnApps.alternative.name]} သို့ထည့်ရန်
              </button>
            {/if}

            <div class="mt-3 text-sm text-gray-500">
                ဤခလုတ်သည် IPမန်း လင့်ကို App ထဲအော်တိုထည့်ပေးပါမည်။
            </div>
          </div>

          <!-- Step 3: Connect -->
          <div class="p-4 border rounded-lg bg-gray-50">
            <h3 class="text-3xl font-medium flex items-center">
              <span class="flex items-center justify-center bg-primary text-white rounded-full w-6 h-6 mr-2 text-sm">3</span>
              အသုံးပြုမည်
            </h3>
            <p class="mt-2 text-gray-600 mb-4">သက်ဆိုင်ရာ App မှာတပ်ဆင် အသုံးပြုနည်း Video ကိုကြည့်ပါ။</p>

            {#if selectedApp === 'primary'}
              <button
                class="flex items-center px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                onclick={() => showTutorial(currentPlatform, 'primary')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                Video ကြည့်ရန်
              </button>
            {:else}
              <button
                class="flex items-center px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                onclick={() => showTutorial(currentPlatform, 'alternative')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                Video ကြည့်ရန်
              </button>
            {/if}
          </div>
        </div>
      </div>

      <!-- Support link -->
      <div class="mt-6 text-center">
        <a
          href="https://t.me/ipmanmyanmar"
          target="_blank"
          rel="noopener noreferrer"
          class="inline-flex items-center text-primary hover:underline"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4.64 6.8c-.15 1.58-.8 5.42-1.13 7.19-.14.75-.42 1-.68 1.03-.58.05-1.02-.38-1.58-.75-.88-.58-1.38-.94-2.23-1.5-.99-.65-.35-1.01.22-1.59.15-.15 2.71-2.48 2.76-2.69.01-.03.01-.14-.05-.2-.06-.06-.16-.04-.23-.02-.1.02-1.62 1.03-4.58 3.03-.43.3-.82.45-1.17.44-.39-.01-1.13-.22-1.68-.4-.68-.23-1.22-.35-1.17-.74.02-.2.3-.4.79-.6 3.16-1.37 5.26-2.27 6.3-2.71 3-.126 3.63-1.505 3.63-1.505z"/>
          </svg>
          Telegram Support
        </a>
      </div>

      <!-- Video Tutorial Modal -->
      {#if showVideoTutorial && selectedPlatform}
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl">
            <div class="p-4 border-b flex justify-between items-center">
              <h3 class="text-lg font-medium">
                {selectedApp === 'primary'
                  ? vpnApps.primary.name[selectedPlatform as keyof typeof vpnApps.primary.name]
                  : vpnApps.alternative.name[selectedPlatform as keyof typeof vpnApps.alternative.name]}
                တွင် {selectedPlatform.charAt(0).toUpperCase() + selectedPlatform.slice(1)} တပ်ဆင်နည်း
              </h3>
              <button
              class="text-gray-500 hover:text-gray-700"
              onclick={closeTutorial}
              aria-label="Close tutorial"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            </div>
            <div class="p-0 aspect-video">
              <iframe
                class="w-full h-full"
                src={getTutorialUrl(selectedPlatform, selectedApp)}
                title="Video tutorial"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen
              ></iframe>
            </div>
          </div>
        </div>
      {/if}
    {:else}
      <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-4" role="alert">
        <strong class="font-bold">No Data!</strong>
        <span class="block sm:inline"> No profile data available.</span>
      </div>
    {/if}
  </div>
</div>
