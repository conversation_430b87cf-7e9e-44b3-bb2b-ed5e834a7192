import type { PageServerLoad } from './$types';
import { wooCommerceService } from '$lib/server/woocommerce.server'; // Import from server-only module
import { env } from '$env/dynamic/private';
import type { Product } from '$lib/services/woocommerce'; // Import type

// Define IDs for the specific plans needed on the homepage
const BASIC_PLAN_ID = 141;
const GOLDEN_PLAN_ID = 4677;

export const load: PageServerLoad = async () => {
  const products: Product[] = [];
  let apiError: string | null = null;
  let generalError: string | null = null;

  try {
    // Check if WooCommerce API is configured using private env vars
    const apiConfigured = !!env.WOOCOMMERCE_URL && 
                         !!env.WOOCOMMERCE_CONSUMER_KEY && 
                         !!env.WOOCOMMERCE_CONSUMER_SECRET;
    
    if (!apiConfigured) {
      console.warn('WooCommerce API not configured. Please set up your API credentials in .env');
      return {
        products: [],
        apiConfigured: false,
        apiError: 'API not configured.' // Provide specific feedback
      };
    }
    
    // Fetch specific products needed for the homepage
    try {
      console.log(`[Server] Fetching homepage plans: Basic (${BASIC_PLAN_ID}), Golden (${GOLDEN_PLAN_ID})`);
      const basicPlanPromise = wooCommerceService.getProduct(BASIC_PLAN_ID);
      const goldenPlanPromise = wooCommerceService.getProduct(GOLDEN_PLAN_ID);

      const [basicPlan, goldenPlan] = await Promise.all([basicPlanPromise, goldenPlanPromise]);

      if (basicPlan && basicPlan.status === 'publish') {
        products.push(basicPlan);
      } else {
         console.warn(`Basic Plan (ID: ${BASIC_PLAN_ID}) not found or not published.`);
      }

      if (goldenPlan && goldenPlan.status === 'publish') {
        products.push(goldenPlan);
      } else {
         console.warn(`Golden Plan (ID: ${GOLDEN_PLAN_ID}) not found or not published.`);
      }
      
      console.log(`[Server] Found ${products.length} published homepage plans.`);
      
      return {
        products,
        apiConfigured: true,
        apiError: null, // Explicitly null if successful
        error: null
      };

    } catch (err) {
      console.error('[Server] WooCommerce API error fetching homepage products:', err);
      apiError = err instanceof Error ? err.message : 'Unknown API error fetching homepage products';
      // Return empty products but indicate API error
      return {
        products: [],
        apiConfigured: true, // API is configured, but the call failed
        apiError,
        error: null
      };
    }
  } catch (err) {
    console.error('[Server] Error in homepage load function:', err);
    generalError = err instanceof Error ? err.message : 'Unknown error loading homepage';
    // Return empty data and general error
    return {
      products: [],
      apiConfigured: false, // Assume configuration might be part of the issue if we reach here
      apiError: null,
      error: generalError
    };
  }
};
