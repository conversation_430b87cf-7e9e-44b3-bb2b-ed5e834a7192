# Caddyfile configuration

# Global options block
{
    # Email address for ACME certificate registration (Let's Encrypt)
    # Replace with your actual email address
    email <EMAIL>
}

# Define the site address using an environment variable or hardcode your domain
{$YOUR_DOMAIN:localhost} {
    # Enable reverse proxy logging
    log {
        output stderr
        format json
    }

    # Route requests starting with /ocr-test
    route /ocr-test* {
        # Apply HTTP Basic Authentication
        basicauth {
            # Use environment variables for username and hashed password
            {$CADDY_ADMIN_USER} {$CADDY_ADMIN_PASSWORD_HASH}
        }
        # If authentication succeeds, proxy to the SvelteKit app service
        reverse_proxy app:3000
    }

    # Handle all other requests by proxying to the SvelteKit app service
    reverse_proxy app:3000 {
        # Optionally add headers if needed, e.g., for websockets
        # header_up X-Forwarded-Proto {scheme}
        # header_up Host {host}
    }

    # Enable gzip and zstd compression
    encode gzip zstd

    # Optional: Configure file server if <PERSON><PERSON><PERSON> should serve static files directly
    # This might conflict with how adapter-node serves static files, use with caution
    # root * /srv # Assuming static files are copied to /srv in Caddy container
    # file_server
}

# You can add more site blocks here for other domains or subdomains
