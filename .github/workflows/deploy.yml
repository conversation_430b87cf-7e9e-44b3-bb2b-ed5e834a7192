name: Build and Deploy to CapRover


on:
  push:
    branches:
      - main # Or your primary deployment branch
  workflow_dispatch: # Allows manual triggering

env:
  REGISTRY: ghcr.io
  # github.repository is in the format owner/repository-name
  # IMAGE_NAME_BASE will be set in a step below

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: read      # For actions/checkout
      packages: write    # For pushing to GHCR
      # id-token: write # Uncomment if using OIDC for authentication elsewhere

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set Lowercase Image Name
        run: echo "LOWERCASE_IMAGE_NAME=$(echo ${{ github.repository }} | tr '[:upper:]' '[:lower:]')" >> $GITHUB_ENV

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.LOWERCASE_IMAGE_NAME }}
          # Generates tags like:
          # - main (if on main branch)
          # - sha-xxxxxxx
          # - latest (if on default branch)

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          platforms: linux/amd64 # Add other platforms if needed, e.g., linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            # No longer passing SvelteKit specific static env vars as build-args.
            # These will be runtime environment variables set in CapRover.
            # Add any other non-SvelteKit-static build arguments here if needed in the future.
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Deploy to CapRover
        uses: caprover/deploy-from-github@v1.1.2
        with:
          server: '${{ secrets.CAPROVER_SERVER }}'
          app: '${{ secrets.CAPROVER_APP_NAME }}'
          token: '${{ secrets.CAPROVER_APP_TOKEN }}'
          # Deploy the image tagged with 'main' from the main branch
          image: '${{ env.REGISTRY }}/${{ env.LOWERCASE_IMAGE_NAME }}:main'
