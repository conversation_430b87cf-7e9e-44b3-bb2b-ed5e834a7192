<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Golden VPN Profile</title>
    <link
      href="https://cdn.prod.website-files.com/6777c17452678d1c4df8b828/6780f7a44edbe28003978503_siteTinyMini.png"
      rel="shortcut icon"
      type="image/x-icon"
    />
    <link
      href="https://cdn.prod.website-files.com/6777c17452678d1c4df8b828/6780f79f909a8fa69b2f0091_siteTiny.png"
      rel="apple-touch-icon"
    />
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
      /* Font Definitions - Using CDN links from original, ensure names match Tai<PERSON><PERSON> config */
      @font-face {
        font-family: 'Z11-MyanSans';
        src: url("https://cdn.prod.website-files.com/6777c17452678d1c4df8b828/677ce4d731730cbded75ac95_Z11-MyanSans-Bold.ttf") format('truetype');
        font-weight: 700;
        font-style: normal;
        font-display: swap;
      }
      @font-face {
        font-family: 'Z11-MyanSans';
        src: url("https://cdn.prod.website-files.com/6777c17452678d1c4df8b828/677ce4d77aba5fbbc2fe558b_Z11-MyanSans-Regular.ttf") format('truetype');
        font-weight: 400;
        font-style: normal;
        font-display: swap;
      }
      @font-face {
        font-family: 'Z11-MyanSans';
        src: url("https://cdn.prod.website-files.com/6777c17452678d1c4df8b828/677ce4d77e11541d06a6b470_Z11-MyanSans-Thin.ttf") format('truetype');
        font-weight: 100;
        font-style: normal;
        font-display: swap;
      }
      @font-face {
        font-family: 'Z17Strength';
        src: url("https://cdn.prod.website-files.com/6777c17452678d1c4df8b828/677ce501733a9c528d5d584e_A03_Octagon-Regular.ttf") format('truetype');
        font-weight: 400;
        font-style: normal;
        font-display: swap;
      }

      :root {
        --background: #D8D8F2;
        --text: #271f30;
        --shadow: rgba(0, 0, 0, 0.1);
        --color-primary: #7338f2;
        --color-primary-dark: #5f2dc8;
        --color-primary-light: #9c6ff7;
        --color-gold: #ffd700;
        --color-gold-dark: #e6c300;
        --color-card-bg: #e4e2ff;
        --color-card-border: #d1d5db;
        --color-muted-text: #6b7280;
        --color-red: #ff0000;
        --gold-gradient: linear-gradient(45deg, var(--color-gold), #fff8e1, var(--color-gold));
      }

      /* Dark mode styles now triggered by .dark-mode class on html element */
      html.dark-mode {
        --background: #271f30;
        --text: #e4e2ff;
        --shadow: rgba(0, 0, 0, 0.25);
        --color-primary: #9c6ff7;
        --color-primary-dark: #7d4df5;
        --color-card-bg: #3a314d;
        --color-card-border: #4f4c7a;
        --color-muted-text: #a0aec0;
        --color-gold: #ffeb7a;
      }

      * { box-sizing: border-box; margin: 0; padding: 0; }
      body {
        background-color: var(--background); color: var(--text);
        font-family: 'Z11-MyanSans', Arial, sans-serif; line-height: 1.6;
        padding: 20px; min-height: 100vh; display: flex; flex-direction: column;
        align-items: center; transition: background-color 0.3s ease;
      }
      h1, h2, h3, h4, h5, h6 { font-family: 'Z17Strength', Arial, sans-serif; font-weight: normal; }
      img { max-width: 100%; height: auto; display: block; }

      .container {
        width: 100%; max-width: 700px; background-color: var(--color-card-bg);
        border-radius: 15px; box-shadow: 0 5px 15px var(--shadow), 0 0 0 1px rgba(115, 56, 242, 0.1);
        padding: 25px; position: relative; transition: all 0.3s; overflow: hidden;
      }
      .container::before {
        content: ""; position: absolute; top: 0; left: 0; right: 0; height: 4px;
        background: linear-gradient(90deg, var(--color-primary-light), var(--color-primary));
      }

      .header-section { text-align: center; margin-bottom: 25px; position: relative; }
      .header-section .logo { width: 180px; margin: 0 auto 20px auto; }
      .header-section .greeting {
        font-size: 2.8em; color: var(--color-primary); margin-bottom: 10px;
        font-weight: normal; display: flex; align-items: center; justify-content: center;
        font-family: 'Z17Strength', Arial, sans-serif;
      }
      /* .gold-icon-badge styles removed as the element is no longer used */
      
      .header-section .user-note, .header-section .order-number, .header-section .usage-summary {
        font-size: 1.05em; color: var(--text); margin-bottom: 5px;
      }
      .header-section .user-note strong, .header-section .order-number strong, .header-section .usage-summary strong {
        color: var(--color-primary); font-weight: bold;
      }
      
      .usage-section { margin-bottom: 25px; }
      .progress-bar-container {
        background-color: var(--background); border-radius: 10px; height: 40px;
        overflow: hidden; border: 1px solid var(--color-primary);
        box-shadow: inset 0 3px 5px rgba(0,0,0,0.07);
      }
      .progress-bar-fill {
        background: linear-gradient(90deg, var(--color-primary-light), var(--color-primary));
        height: 100%; width: 0%; border-radius: 8px;
        transition: width 0.8s ease-in-out;
        position: relative;
        overflow: hidden;
      }
      .progress-bar-fill::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
          animation: shimmer-effect 2.5s ease-in-out infinite;
      }
      @keyframes shimmer-effect {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
      }
      .usage-percentage-text { text-align: right; font-size: 0.85em; color: var(--color-muted-text); margin-top: 5px; }

      .dark-mode-toggle {
        position: absolute; top: 15px; right: 15px; background-color: var(--color-card-bg);
        border-radius: 50%; width: 38px; height: 38px; padding: 7px; cursor: pointer;
        border: 1px solid var(--color-primary); box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: all 0.3s;
        z-index: 10; /* Ensure it's above other elements */
      }
      .dark-mode-toggle:hover { transform: rotate(15deg); box-shadow: 0 2px 8px var(--shadow); }
      .dark-mode-toggle img { width: 100%; height: 100%; }
      
      .main-tabs {
        display: flex; justify-content: space-around; margin-bottom: 20px;
        background-color: var(--background); border-radius: 10px; padding: 5px;
      }
      .main-tab-button {
        background-color: transparent; color: var(--color-muted-text); border: none;
        padding: 10px 15px; font-size: 0.95em; font-weight: bold; cursor: pointer;
        border-radius: 8px; transition: all 0.3s; flex-grow: 1; text-align: center;
      }
      .main-tab-button i { margin-right: 5px; }
      .main-tab-button:hover { color: var(--color-primary); }
      .main-tab-button.active { background-color: var(--color-primary); color: white; }

      .tab-content {
        display: none; padding: 20px; border: 1px solid var(--color-card-border);
        border-radius: 10px; background-color: var(--color-card-bg);
        margin-bottom: 20px; box-shadow: 0 2px 8px var(--shadow);
      }
      .tab-content.active { display: block; }
      .tab-content h2 {
        color: var(--color-primary); margin-bottom: 20px; font-size: 1.4em;
        border-bottom: 1px solid var(--color-card-border); padding-bottom: 10px;
        font-weight: normal; display: flex; align-items: center;
      }
      .tab-content h2 i { margin-right: 8px; }

      .action-button {
        display: inline-flex; align-items: center; justify-content: center;
        background-color: var(--color-primary); color: white; padding: 10px 20px;
        border-radius: 8px; text-decoration: none; font-weight: bold;
        transition: all 0.3s; border: none; cursor: pointer; font-size: 1em;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      .action-button:hover {
        background-image: linear-gradient(to bottom, var(--color-primary-light), var(--color-primary));
        transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2);
      }
      .action-button i { margin-right: 6px; }
      
      .platform-tabs { display: flex; margin-bottom: 20px; border-bottom: 1px solid var(--color-card-border); }
      .platform-tab-button {
        background-color: transparent; color: var(--color-muted-text); border: none;
        padding: 8px 12px; font-size: 0.9em; cursor: pointer;
        border-bottom: 3px solid transparent; margin-right: 10px; transition: all 0.3s;
        display: flex; align-items: center;
      }
      .platform-tab-button i { margin-right: 5px; }
      .platform-tab-button:hover { color: var(--color-primary); }
      .platform-tab-button.active { color: var(--color-primary); border-bottom-color: var(--color-primary); }
      .platform-content { display: none; } /* App cards will be injected here by JS */
      .platform-content.active { display: block; }

      .app-card {
        background-color: var(--background); padding: 20px; border-radius: 8px;
        margin-bottom: 15px; border: 1px solid var(--color-card-border); transition: all 0.3s;
      }
      .app-card:hover {
        transform: translateY(-3px) scale(1.01);
        box-shadow: 0 8px 20px var(--shadow);
      }
      /* .app-card.recommended styles and ::before (ribbon) styles removed */
      .app-card h3 {
        font-weight: bold; font-size: 1.15em; color: var(--text); margin-bottom: 15px;
        font-family: 'Z11-MyanSans', Arial, sans-serif; display: flex; align-items: center;
      }
      .app-card h3 i { margin-right: 8px; color: var(--color-primary); }
      .app-card .app-status { font-style: italic; color: var(--color-muted-text); font-size: 0.9em; }

      .steps-container { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-bottom: 10px; }
      .step-button {
        background-color: var(--color-card-bg); color: var(--color-primary); padding: 10px;
        font-weight: 500; font-size: 0.9em; border: 1px solid var(--color-primary);
        border-radius: 8px; text-decoration: none; text-align: center; cursor: pointer;
        transition: all 0.3s; display: flex; align-items: center; justify-content: center; width: 100%;
      }
      .step-button i { margin-right: 5px; }
      .step-button:hover {
        background-color: var(--color-primary); color: white;
        transform: translateY(-2px); box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      }
      .step-button.video-trigger { display: inline-flex; align-items: center; justify-content: center; }
      .step-button.video-trigger::after { content: "▶"; margin-left: 5px; font-size: 0.8em; }

      .qr-section { text-align: center; padding: 10px 0; }
      .qr-container {
        width: 180px; height: 180px; margin: 0 auto 20px auto; background-color: white;
        padding: 10px; border-radius: 8px; display: flex; align-items: center;
        justify-content: center; box-shadow: 0 4px 10px rgba(0,0,0,0.15);
        border: 1px solid var(--color-card-border);
      }
      .qr-container canvas, .qr-container img { border-radius: 4px; }
      .copy-link-button .copied-text { display: none; margin-left: 8px; color: var(--color-primary-dark); }
      .copy-link-button.copied .copied-text { display: inline; }
      .copy-link-button.copied .copy-text { display: none; }

      .gb-plus-section { text-align: center; }
      .gb-plus-section .plan-selection {
        display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 20px; justify-content: center;
      }
      .gb-plan-button {
        background-color: var(--color-card-bg); border: 2px solid var(--color-primary);
        color: var(--color-primary); padding: 12px 15px; border-radius: 8px;
        cursor: pointer; font-weight: bold; transition: all 0.3s; position: relative; overflow: hidden;
      }
      .gb-plan-button i { margin-right: 5px; }
      .gb-plan-button::before {
        content: ""; position: absolute; top: -2px; left: -2px; right: -2px; bottom: -2px;
        border-radius: 8px; background: linear-gradient(45deg, var(--color-primary), var(--color-primary-dark), var(--color-primary));
        z-index: -2; opacity: 0; transition: opacity 0.3s ease;
      }
      .gb-plan-button:hover::before { opacity: 0.2; }
      .gb-plan-button:hover, .gb-plan-button.selected {
        background-color: var(--color-primary); color: white;
        transform: translateY(-2px); box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      }
      
      .payment-info-section {
        background-color: var(--background); border: 1px solid var(--color-primary);
        border-radius: 8px; padding: 15px; margin-bottom: 20px; position: relative; overflow: hidden;
      }
      .payment-info-section::before {
        content: ""; position: absolute; height: 100%; width: 4px;
        background-color: var(--color-primary); left: 0; top: 0;
      }
      .payment-info-section h4 {
        display: flex; align-items: center; color: var(--color-primary);
        font-size: 1.2em; margin-bottom: 12px; font-weight: normal;
      }
      .payment-info-content { margin-left: 15px; }
      .payment-info-row { display: flex; align-items: center; margin-bottom: 8px; color: var(--text); }
      .payment-info-row i { margin-right: 8px; flex-shrink: 0; color: var(--color-primary); width: 16px; text-align: center; }
      .payment-info-label { font-size: 0.9em; margin-right: 5px; }
      .payment-info-value { font-weight: bold; }
      .payment-info-note { font-size: 0.8em; color: var(--color-muted-text); font-style: italic; margin-top: 8px; margin-left: 15px; }
      .payment-info-note-red { font-size: 0.8em; color: var(--color-red); font-style: italic; margin-top: 8px; margin-left: 15px; }

      .total-cost {
        text-align: center; font-size: 1.2em; margin-bottom: 20px; color: var(--text);
        position: relative; padding: 10px 0; border-top: 1px dashed var(--color-card-border);
        border-bottom: 1px dashed var(--color-card-border);
      }
      .total-cost strong { color: var(--color-primary); font-size: 1.1em; }
      
      .upload-zone {
        border: 2px dashed var(--color-primary); border-radius: 8px; padding: 25px;
        text-align: center; color: var(--color-muted-text); cursor: pointer;
        margin-bottom: 20px; background-color: var(--background); transition: all 0.3s; position: relative;
      }
      .upload-zone::before {
        content: "\f574"; font-family: "Font Awesome 6 Free"; font-weight: 900;
        font-size: 24px; color: var(--color-primary); opacity: 0.5; display: block; margin-bottom: 10px;
      }
      .upload-zone:hover {
        border-color: var(--color-primary-dark); color: var(--color-primary);
        background-color: rgba(115, 56, 242, 0.05);
      }
      .dropzone-preview-container { margin-top: 10px; }
      .dz-preview {
        background: var(--background); border-radius: 5px; padding: 8px; margin-top: 5px;
        font-size: 0.9em; display: flex; justify-content: space-between; align-items: center;
        border: 1px solid var(--color-card-border);
      }
      .dz-filename span { color: var(--text); }
      .dz-size span { color: var(--color-muted-text); }
      .dz-remove { color: #FF6B6B; text-decoration: none; font-weight: bold; }

      .video-modal-overlay {
        position: fixed; inset: 0; background-color: rgba(0,0,0,0.6);
        display: flex; align-items: center; justify-content: center;
        z-index: 1000; opacity: 0; visibility: hidden; transition: opacity 0.3s, visibility 0.3s;
      }
      .video-modal-overlay.active { opacity: 1; visibility: visible; }
      .video-modal-content {
        background-color: var(--color-card-bg); padding: 20px; border-radius: 10px;
        width: 90%; max-width: 800px; position: relative; box-shadow: 0 5px 20px rgba(0,0,0,0.2);
      }
      .video-modal-close {
        position: absolute; top: -10px; right: -10px; background: var(--color-primary);
        border: none; color: white; font-size: 1em; cursor: pointer; border-radius: 50%;
        width: 30px; height: 30px; display: flex; align-items: center; justify-content: center;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
      }
      .video-wrapper { position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden; border-radius: 5px; }
      .video-wrapper iframe, .video-wrapper video { position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none; }

      .loading-overlay {
        position: fixed; inset: 0; background-color: rgba(0,0,0,0.5);
        display: flex; align-items: center; justify-content: center; z-index: 2000; display: none;
      }
      .loading-animation { width: 100px; height: 100px; }

      @media screen and (max-width: 767px) {
        body { padding: 10px; } .container { padding: 20px 15px; }
        .header-section .greeting { font-size: 2.5em; } .main-tabs { flex-direction: column; }
        .main-tab-button { margin-bottom: 5px; }
        .platform-tabs { flex-wrap: wrap; justify-content: center; }
        .platform-tab-button { font-size: 0.85em; margin-bottom: 8px;}
        .steps-container { grid-template-columns: 1fr; gap: 10px; }
      }
      @media screen and (max-width: 479px) {
        .header-section .logo { width: 140px; } .header-section .greeting { font-size: 2.5em; }
        .header-section .user-note, .header-section .order-number, .header-section .usage-summary { font-size: 0.95em; }
        .dark-mode-toggle { top: 10px; right: 10px; width: 35px; height: 35px; padding: 6px;}
        .gb-plus-section .plan-selection { justify-content: space-between; }
        .gb-plan-button { flex-basis: calc(50% - 5px); font-size: 0.85em; padding: 8px 10px; }
        .tab-content h2 { font-size: 1.25em; } .action-button { font-size: 0.9em; padding: 8px 15px; }
      }
    </style>
</head>
<body>
    <div class="container">
        <div class="dark-mode-toggle" id="darkModeToggle">
            <img src="https://cdn.prod.website-files.com/6777c17452678d1c4df8b828/677fd082dcb4dda99fde4ae2_bulb-3655.svg" alt="Toggle Dark Mode" />
        </div>

        <header class="header-section">
            <img src="https://cdn.prod.website-files.com/6777c17452678d1c4df8b828/677fd8f300269f526bf28554_MyLogoSVG.svg" alt="Logo" class="logo" />
            <div class="greeting">မင်္ဂလာပါ</div>
            <div class="user-note"><strong>{{ user.note }}</strong></div>
            <div class="order-number">Order အမှတ် - <strong>{{ user.username }}</strong></div>
            <div class="usage-summary">သုံးပြီး - <strong id="usedTrafficFormatted">{{ user.used_traffic | bytesformat }}</strong></div>
            <div class="usage-summary">စုစုပေါင်း - <strong id="totalTrafficFormatted">{{ user.data_limit | bytesformat }}</strong></div>
        </header>

        <section class="usage-section">
            <div class="progress-bar-container">
                <div class="progress-bar-fill"></div>
            </div>
            <div class="usage-percentage-text"></div>
        </section>

        <nav class="main-tabs">
            <button class="main-tab-button active" data-tab="howToUse"><i class="fas fa-book-open fa-sm"></i> အသုံးပြုရန်</button>
            <button class="main-tab-button" data-tab="linkQr"><i class="fas fa-qrcode fa-sm"></i> လင့်/QR</button>
            <button class="main-tab-button" data-tab="gbPlus"><i class="fas fa-plus-circle fa-sm"></i> GB တိုး</button>
        </nav>

        <main>
            <section id="howToUseTab" class="tab-content active">
                <h2><i class="fas fa-book"></i> အသုံးပြုနည်း</h2>
                <nav class="platform-tabs">
                    <button class="platform-tab-button active" data-platform="ios"><i class="fab fa-apple"></i> iPhone</button>
                    <button class="platform-tab-button" data-platform="android"><i class="fab fa-android"></i> Android</button>
                    <button class="platform-tab-button" data-platform="windows"><i class="fab fa-windows"></i> Windows</button>
                    <button class="platform-tab-button" data-platform="macos"><i class="fab fa-apple"></i> MacOS</button>
                </nav>
                <!-- App cards will be dynamically injected here -->
                <div id="iosPlatform" class="platform-content active"></div>
                <div id="androidPlatform" class="platform-content"></div>
                <div id="windowsPlatform" class="platform-content"></div>
                <div id="macosPlatform" class="platform-content"></div>
            </section>
            <section id="linkQrTab" class="tab-content">
                <h2><i class="fas fa-qrcode"></i> လင့် နှင့် QR Code</h2>
                <div class="qr-section">
                    <div class="qr-container"></div>
                    <button class="action-button copy-link-button"><i class="fas fa-copy"></i><span class="copy-text">COPY LINK</span><span class="copied-text">COPIED!</span></button>
                </div>
            </section>
            <section id="gbPlusTab" class="tab-content">
                <h2><i class="fas fa-plus-circle"></i> GB တိုးရန်</h2>
                <div class="gb-plus-section">
                    <div class="plan-selection">
                        <button class="gb-plan-button" data-gb="50" data-amount="9800"><i class="fas fa-database"></i> 50 GB</button>
                        <button class="gb-plan-button" data-gb="100" data-amount="15800"><i class="fas fa-database"></i> 100 GB</button>
                        <button class="gb-plan-button" data-gb="300" data-amount="29800"><i class="fas fa-database"></i> 300 GB</button>
                        <button class="gb-plan-button" data-gb="500" data-amount="39800"><i class="fas fa-database"></i> 500 GB</button>
                    </div>
                    <div class="payment-info-section">
                        <h4><i class="fas fa-wallet"></i>&nbsp;ငွေလွှဲရန်-</h4>
                        <div class="payment-info-content">
                            <div class="payment-info-row"><i class="fas fa-credit-card"></i><span class="payment-info-label">KBZ Pay / Wave Pay</span></div>
                            <div class="payment-info-row"><i class="fas fa-user"></i><span class="payment-info-label">နာမည်-</span><span class="payment-info-value">Khin Maung Phyo</span></div>
                            <div class="payment-info-row"><i class="fas fa-phone"></i><span class="payment-info-label">ဖုန်း-</span><span class="payment-info-value">09785968290</span></div>
                        </div>
                        <p class="payment-info-note">* ဖုန်းခေါ်ဆိုမှုများ လက်ခံမည်မဟုတ်ပါ။ ငွေလွှဲရန်သာ အသုံးပြုပါ။</p>
                        <p class="payment-info-note-red">* ငွေလွှဲမှတ်ချက်တွင် "IPမန်း , IPman, VPN" အစရှိသောစကားလုံးများ "မရေး" ပါနှင့်</p>
                    </div>
                    <div class="total-cost">Total: <strong>0 ကျပ်</strong></div>
                    <div id="dropzoneUpload" class="upload-zone">ပြေစာ(SS) ဒီမှာထည့်ပါ</div>
                    <div id="dropzonePreview" class="dropzone-preview-container"></div>
                    <button class="action-button" id="submitGbPlus"><i class="fas fa-arrow-right"></i> တိုးမည်</button>
                </div>
            </section>
        </main>
    </div>

    <div id="videoModalV2boxIos" class="video-modal-overlay">
        <div class="video-modal-content"><button class="video-modal-close" data-close-modal>&times;</button><div class="video-wrapper"><video class="js-player" playsinline controls title="V2Box iOS Tutorial"><source src="https://player.vimeo.com/video/1081820267" type="video/mp4" /></video></div></div>
    </div>
    <div id="videoModalStreisandIos" class="video-modal-overlay">
        <div class="video-modal-content"><button class="video-modal-close" data-close-modal>&times;</button><div class="video-wrapper"><video class="js-player" playsinline controls title="Streisand iOS Tutorial"><source src="https://player.vimeo.com/video/Streisand.mp4" type="video/mp4" /></video></div></div>
    </div>
    <div id="videoModalHiddifyIos" class="video-modal-overlay">
        <div class="video-modal-content"><button class="video-modal-close" data-close-modal>&times;</button><div class="video-wrapper"><video class="js-player" playsinline controls title="Hiddify iOS Tutorial"><source src="https://player.vimeo.com/video/1081817047" type="video/mp4" /></video></div></div>
    </div>
    <div id="videoModalIpmanAndroid" class="video-modal-overlay">
        <div class="video-modal-content"><button class="video-modal-close" data-close-modal>&times;</button><div class="video-wrapper"><video class="js-player" playsinline controls title="IPman Android Tutorial"><source src="https://player.vimeo.com/video/1081820034" type="video/mp4" /></video></div></div>
    </div>
    <div id="videoModalV2boxAndroid" class="video-modal-overlay"> 
        <div class="video-modal-content"><button class="video-modal-close" data-close-modal>&times;</button><div class="video-wrapper"><video class="js-player" playsinline controls title="V2Box Android Tutorial"><source src="https://player.vimeo.com/video/V2boxPhone.mp4" type="video/mp4" /></video></div></div>
    </div>
    <div id="videoModalHiddifyAndroid" class="video-modal-overlay"> 
        <div class="video-modal-content"><button class="video-modal-close" data-close-modal>&times;</button><div class="video-wrapper"><video class="js-player" playsinline controls title="Hiddify Android Tutorial"><source src="https://player.vimeo.com/video/1081817047" type="video/mp4" /></video></div></div>
    </div>
    <div id="videoModalHiddifyPc" class="video-modal-overlay">
        <div class="video-modal-content"><button class="video-modal-close" data-close-modal>&times;</button><div class="video-wrapper"><video class="js-player" playsinline controls title="Hiddify PC Tutorial"><source src="https://player.vimeo.com/video/1081817978" type="video/mp4" /></video></div></div>
    </div>

    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-animation" data-animation-type="lottie" data-src="https://cdn.prod.website-files.com/6777c17452678d1c4df8b828/6783bb7a8f24c07f41f0c011_myloadinganim.json" data-loop="1" data-direction="1" data-autoplay="1" data-is-ix2-target="0" data-renderer="svg"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/gh/MuhammadAshouri/marzban-templates@master/template-01/qrcode.min.js"></script>
    <script src="https://unpkg.com/dropzone@5/dist/min/dropzone.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.12.2/lottie.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/js-sha256/0.9.0/sha256.min.js"></script>


    <script>
      // APP CONFIGURATIONS - Edit this object to manage app cards
      const appConfigurations = {
        ios: [
          {
            name: "Hiddify",
            iconClass: "fas fa-ghost",
            steps: [
              { type: "download", text: '"1" Download', link: "https://apps.apple.com/us/app/hiddify-proxy-vpn/id6596777532", iconClass: "fas fa-download" },
              { type: "import", text: '"2" လင့်ထည့်', linkPattern: "hiddify://import/{{ user.subscription_url }}", iconClass: "fas fa-link" },
              { type: "video", text: '"3" သုံးနည်း', videoId: "hiddify_ios_video", iconClass: "fas fa-video" }
            ]
          },
          {
            name: "V2Box",
            iconClass: "fas fa-star", // Using a generic star, replace with specific app icon if available
            steps: [
              { type: "download", text: '"1" Download', link: "https://apps.apple.com/us/app/v2box-v2ray-client/id6446814690", iconClass: "fas fa-download" },
              { type: "import", text: '"2" လင့်ထည့်', linkPattern: "v2box://install-sub?url={{ user.subscription_url }}&name={{ user.note }}", iconClass: "fas fa-link" },
              { type: "video", text: '"3" သုံးနည်း', videoId: "v2box_ios_video", iconClass: "fas fa-video" }
            ]
          },
          // {
          //   name: "Streisand",
          //   iconClass: "fas fa-shield-alt",
          //   steps: [
          //     { type: "download", text: '"1" Download', link: "https://apps.apple.com/us/app/streisand/id6450534064", iconClass: "fas fa-download" },
          //     { type: "import", text: '"2" လင့်ထည့်', linkPattern: "streisand://import/{{ user.subscription_url }}", iconClass: "fas fa-link" },
          //     { type: "video", text: '"3" သုံးနည်း', videoId: "streisand_ios_video", iconClass: "fas fa-video" }
          //   ]
          // }
        ],
        android: [
          {
            name: "IPman",
            iconClass: "fas fa-star",
            steps: [
              { type: "download", text: '"1" Download', link: "https://intarnad.com/IPman.apk", iconClass: "fas fa-download" },
              { type: "import", text: '"2" လင့်ထည့်', linkPattern: "ipman://import/{{ user.subscription_url }}", iconClass: "fas fa-link" }, // Assuming IPman uses hiddify protocol for import
              { type: "video", text: '"3" သုံးနည်း', videoId: "ipman_android_video", iconClass: "fas fa-video" }
            ]
          },
          {
            name: "V2Box",
            iconClass: "fas fa-shield-alt",
            steps: [
              { type: "download", text: '"1" Download', link: "https://play.google.com/store/apps/details?id=dev.hexasoftware.v2box", iconClass: "fas fa-download" },
              { type: "import", text: '"2" လင့်ထည့်', linkPattern: "v2box://install-config?url={{ user.subscription_url }}", iconClass: "fas fa-link" },
              { type: "video", text: '"3" သုံးနည်း', videoId: "v2box_android_video", iconClass: "fas fa-video" }
            ]
          },
          {
            name: "Hiddify",
            iconClass: "fas fa-ghost",
            steps: [
              { type: "download", text: '"1" Download', link: "https://play.google.com/store/apps/details?id=com.hiddify.hiddify", iconClass: "fas fa-download" },
              { type: "import", text: '"2" လင့်ထည့်', linkPattern: "hiddify://import/{{ user.subscription_url }}", iconClass: "fas fa-link" },
              { type: "video", text: '"3" သုံးနည်း', videoId: "hiddify_android_video", iconClass: "fas fa-video" }
            ]
          }
        ],
        windows: [
          {
            name: "IPman",
            iconClass: "fas fa-star",
            steps: [
              { type: "download", text: '"1" Download', link: "https://intarnad.com/IPman.exe", iconClass: "fas fa-download" },
              { type: "import", text: '"2" လင့်ထည့်', linkPattern: "ipman://import/{{ user.subscription_url }}", iconClass: "fas fa-link" }, // Assuming IPman uses hiddify protocol for import
              { type: "video", text: '"3" သုံးနည်း', videoId: "ipman_android_video", iconClass: "fas fa-video" }
            ]
          },
          {
            name: "Hiddify",
            iconClass: "fas fa-ghost",
            steps: [
              { type: "download", text: '"1" Download', link: "https://apps.microsoft.com/detail/9pdfnl3qv2s5?ocid=webpdpshare", iconClass: "fas fa-download" },
              { type: "import", text: '"2" လင့်ထည့်', linkPattern: "hiddify://import/{{ user.subscription_url }}", iconClass: "fas fa-link" },
              { type: "video", text: '"3" သုံးနည်း', videoId: "hiddify_pc_video", iconClass: "fas fa-video" }
            ]
          }
          //{ name: "IPman", iconClass: "fas fa-star", status: "Coming Soon..." }
        ],
        macos: [
          {
            name: "IPman",
            iconClass: "fas fa-star",
            steps: [
              { type: "download", text: '"1" Download', link: "https://intarnad.com/IPman.dmg", iconClass: "fas fa-download" },
              { type: "import", text: '"2" လင့်ထည့်', linkPattern: "ipman://import/{{ user.subscription_url }}", iconClass: "fas fa-link" }, // Assuming IPman uses hiddify protocol for import
              { type: "video", text: '"3" သုံးနည်း', videoId: "ipman_android_video", iconClass: "fas fa-video" }
            ]
          },
          {
            name: "Hiddify",
            iconClass: "fas fa-ghost",
            steps: [
              { type: "download", text: '"1" Download', link: "https://apps.apple.com/us/app/hiddify-proxy-vpn/id6596777532", iconClass: "fas fa-download" },
              { type: "import", text: '"2" လင့်ထည့်', linkPattern: "hiddify://import/{{ user.subscription_url }}", iconClass: "fas fa-link" },
              { type: "video", text: '"3" သုံးနည်း', videoId: "hiddify_pc_video", iconClass: "fas fa-video" }
            ]
          }
          //{ name: "IPman", iconClass: "fas fa-star", status: "Coming Soon..." }
        ]
      };

      document.addEventListener('DOMContentLoaded', function () {
        let currentPaymentProofHash = null; // To store the hash of the current file in Dropzone
        const SUBMITTED_PROOFS_KEY = 'submittedGbPlusProofHashes';
        const RECENCY_WINDOW_MS = 24 * 60 * 60 * 1000; // 24 hours
        const MAX_STORED_HASHES = 20; // Max number of hashes to keep in localStorage
        const HASH_EXPIRY_MS = 7 * 24 * 60 * 60 * 1000; // 7 days for individual hash expiry

        // Dark mode toggle functionality (ultra-simple class toggle)
        const darkModeToggleBtn = document.getElementById('darkModeToggle');
        const htmlDocElement = document.documentElement;

        if (darkModeToggleBtn) {
            htmlDocElement.classList.remove('dark-mode'); 
            darkModeToggleBtn.addEventListener('click', function() {
                htmlDocElement.classList.toggle('dark-mode');
            });
        } else {
            console.error("Dark mode toggle button (#darkModeToggle) not found!");
        }
        
        // Lottie Animation for loading
        const loadingAnimContainer = document.querySelector('.loading-animation[data-animation-type="lottie"]');
        if (loadingAnimContainer) {
            lottie.loadAnimation({
                container: loadingAnimContainer,
                renderer: loadingAnimContainer.dataset.renderer || 'svg',
                loop: loadingAnimContainer.dataset.loop === '1',
                autoplay: loadingAnimContainer.dataset.autoplay === '1',
                path: loadingAnimContainer.dataset.src
            });
        }
        
        function bytesFormat(bytes, decimals = 2) {
            if (!bytes || bytes === 0) return '0 Bytes';
            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        }

        const rawUsedTraffic = parseFloat("{{ user.used_traffic }}") || 0;
        const rawTotalTraffic = parseFloat("{{ user.data_limit }}") || 1;

        document.getElementById('usedTrafficFormatted').textContent = bytesFormat(rawUsedTraffic);
        document.getElementById('totalTrafficFormatted').textContent = bytesFormat(rawTotalTraffic);
        
        const percentage = rawTotalTraffic > 0 ? Math.min((rawUsedTraffic / rawTotalTraffic) * 100, 100) : 0;
        const progressBarFill = document.querySelector('.progress-bar-fill');
        const usagePercentageText = document.querySelector('.usage-percentage-text');
        if (progressBarFill) progressBarFill.style.width = percentage + '%';
        if (usagePercentageText) usagePercentageText.textContent = percentage.toFixed(1) + '% သုံးပြီး';

        const mainTabButtons = document.querySelectorAll('.main-tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        mainTabButtons.forEach(button => {
            button.addEventListener('click', () => {
                mainTabButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                const targetTab = button.dataset.tab;
                tabContents.forEach(content => {
                    const isActive = content.id === targetTab + 'Tab';
                    content.style.display = isActive ? 'block' : 'none';
                    content.classList.toggle('active', isActive);
                });
            });
        });

        const platformTabButtons = document.querySelectorAll('.platform-tab-button');
        const platformContents = document.querySelectorAll('.platform-content'); // These are the containers for app cards
        const userAgent = navigator.userAgent.toLowerCase();
        let detectedPlatform = 'ios'; 
        if (userAgent.includes('android')) detectedPlatform = 'android';
        else if (userAgent.includes('windows')) detectedPlatform = 'windows';
        else if (userAgent.includes('mac os') && !userAgent.includes('iphone') && !userAgent.includes('ipad')) detectedPlatform = 'macos';

        function setActivePlatform(platform) {
            platformTabButtons.forEach(button => button.classList.toggle('active', button.dataset.platform === platform));
            platformContents.forEach(content => {
                const isActive = content.id === platform + 'Platform';
                content.style.display = isActive ? 'block' : 'none'; // Show/hide based on ID
                content.classList.toggle('active', isActive);
            });
        }
        
        // Function to generate app cards HTML
        function generateAppCards() {
            for (const platformKey in appConfigurations) {
                const platformContainer = document.getElementById(`${platformKey}Platform`);
                if (!platformContainer) continue;

                let platformHTML = '';
                appConfigurations[platformKey].forEach(app => {
                    platformHTML += `<div class="app-card">`;
                    platformHTML += `<h3><i class="${app.iconClass}"></i> ${app.name}</h3>`;
                    if (app.status) {
                        platformHTML += `<p class="app-status">${app.status}</p>`;
                    } else if (app.steps) {
                        platformHTML += `<div class="steps-container">`;
                        app.steps.forEach(step => {
                            if (step.type === "download") {
                                platformHTML += `<a href="${step.link}" class="step-button" target="_blank"><i class="${step.iconClass}"></i> ${step.text}</a>`;
                            } else if (step.type === "import") {
                                platformHTML += `<a href="${step.linkPattern}" class="step-button"><i class="${step.iconClass}"></i> ${step.text}</a>`;
                            } else if (step.type === "video") {
                                platformHTML += `<button class="step-button video-trigger" data-video-id="${step.videoId}"><i class="${step.iconClass}"></i> ${step.text}</button>`;
                            }
                        });
                        platformHTML += `</div>`;
                    }
                    platformHTML += `</div>`;
                });
                platformContainer.innerHTML = platformHTML;
            }
            // Re-initialize video triggers after generating cards
            initializeVideoTriggers();
        }

        generateAppCards(); // Call the function to populate app cards
        setActivePlatform(detectedPlatform); // Set initial active platform tab and content

        platformTabButtons.forEach(button => {
            button.addEventListener('click', () => setActivePlatform(button.dataset.platform));
        });

        const qrContainer = document.querySelector('.qr-container');
        const subscriptionUrl = "{{ user.subscription_url }}";
        if (qrContainer && subscriptionUrl) {
            $(qrContainer).qrcode({ text: subscriptionUrl, width: 150, height: 150, radius: 0.5, quiet: 1 });
        }

        const copyButton = document.querySelector('.copy-link-button');
        if (copyButton) {
            copyButton.addEventListener('click', () => {
                navigator.clipboard.writeText(subscriptionUrl).then(() => {
                    copyButton.classList.add('copied');
                    setTimeout(() => copyButton.classList.remove('copied'), 2000);
                }).catch(err => console.error('Failed to copy: ', err));
            });
        }

        // Video Modal Logic (factored into a function for re-use)
        const videoModals = document.querySelectorAll('.video-modal-overlay'); // Get all modals once
        const closeModalButtons = document.querySelectorAll('[data-close-modal]');
        const videoMap = { // This map should correspond to videoId in appConfigurations
            'v2box_ios_video': 'videoModalV2boxIos', 'streisand_ios_video': 'videoModalStreisandIos',
            'hiddify_ios_video': 'videoModalHiddifyIos', 'ipman_android_video': 'videoModalIpmanAndroid',
            'v2box_android_video': 'videoModalV2boxAndroid', 'hiddify_android_video': 'videoModalHiddifyAndroid',
            'hiddify_pc_video': 'videoModalHiddifyPc'
        };

        function initializeVideoTriggers() {
            const videoTriggers = document.querySelectorAll('.video-trigger');
            videoTriggers.forEach(trigger => {
                // Remove existing listener to prevent duplicates if called multiple times
                trigger.replaceWith(trigger.cloneNode(true)); 
            });
            // Re-query and add listeners
            document.querySelectorAll('.video-trigger').forEach(trigger => {
                 trigger.addEventListener('click', () => {
                    const modalId = videoMap[trigger.dataset.videoId];
                    const modal = document.getElementById(modalId);
                    if (modal) modal.classList.add('active');
                });
            });
        }
        
        function closeAndPauseModal(modalElement) {
            if (modalElement) {
                modalElement.classList.remove('active');
                const videoPlayer = modalElement.querySelector('video.js-player');
                if (videoPlayer) videoPlayer.pause();
            }
        }
        closeModalButtons.forEach(button => button.addEventListener('click', () => closeAndPauseModal(button.closest('.video-modal-overlay'))));
        videoModals.forEach(modal => modal.addEventListener('click', function(event) { if (event.target === this) closeAndPauseModal(this); }));
        
        // Initial call for any static triggers (though now all are dynamic)
        initializeVideoTriggers();

        const planButtons = document.querySelectorAll('.gb-plan-button');
        const totalCostEl = document.querySelector('.total-cost strong');
        let selectedPlan = null;
        planButtons.forEach(button => {
            button.addEventListener('click', () => {
                planButtons.forEach(btn => btn.classList.remove('selected'));
                button.classList.add('selected');
                selectedPlan = { gb: button.dataset.gb, amount: button.dataset.amount };
                if (totalCostEl) totalCostEl.textContent = parseInt(selectedPlan.amount).toLocaleString() + ' ကျပ်';
            });
        });

        // --- GB Plus Form Logic with Image Hashing ---
        async function generateFileHash(file) {
            if (!file) return null;
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    try {
                        const arrayBuffer = event.target.result;
                        const hash = sha256.hex(arrayBuffer); // Using sha256.hex for string output
                        resolve(hash);
                    } catch (error) {
                        reject(error);
                    }
                };
                reader.onerror = (error) => reject(error);
                reader.readAsArrayBuffer(file);
            });
        }

        Dropzone.autoDiscover = false;
        let myDropzone;
        const dropzoneUploadEl = document.getElementById('dropzoneUpload');
        const dropzonePreviewEl = document.getElementById('dropzonePreview');
        if (dropzoneUploadEl && dropzonePreviewEl) {
             myDropzone = new Dropzone(dropzoneUploadEl, {
                url: "#", autoProcessQueue: false, maxFiles: 1, acceptedFiles: "image/*",
                addRemoveLinks: true, previewsContainer: dropzonePreviewEl,
                dictDefaultMessage: "ပြေစာ(SS) ဒီမှာထည့်ပါ or Click to browse",
                init: function() {
                    this.on("addedfile", async (file) => {
                        try {
                            currentPaymentProofHash = await generateFileHash(file);
                            console.log("File added, hash:", currentPaymentProofHash);
                        } catch (error) {
                            console.error("Error generating file hash:", error);
                            currentPaymentProofHash = null;
                            alert("Error processing image file. Please try a different image.");
                            this.removeFile(file);
                        }
                    });
                    this.on("removedfile", () => {
                        currentPaymentProofHash = null;
                        console.log("File removed, hash cleared.");
                    });
                    this.on("maxfilesexceeded", function(file) { this.removeAllFiles(); this.addFile(file); }); // Will trigger addedfile again
                    this.on("error", (file, message) => {
                        if (typeof message !== "string" && message.message) message = message.message; 
                        alert(message); this.removeFile(file); // Will trigger removedfile
                    });
                }
            });
        }

        const submitGbPlusButton = document.getElementById('submitGbPlus');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const n8nWebhookUrl = "https://auto.subipman.org/webhook/6cb9c6b6-73bb-4b22-b226-a876e1a599ed"; 
        
        if (submitGbPlusButton) {
            submitGbPlusButton.addEventListener('click', async () => {
                if (!selectedPlan) { alert("ထည့်မည့် (GB) ပမာဏ အရင်ရွေးပေးပါ။"); return; }
                if (!myDropzone || myDropzone.files.length === 0) { alert("ငွေလွှဲပြေစာ SS ထည့်ပေးပါ။"); return; }
                if (!currentPaymentProofHash) {
                    alert("Error တက်သွားသည် ( သိုမဟုတ် ) လုပ်ဆောင်ချက်မပြီးဆုံးသေးပါ။ နောက်တစ်ကြိမ်ထပ်မံကြိုးစားကြည့်ပါ။");
                    return;
                }

                // Check for recent duplicate hash
                let submittedProofs = [];
                try {
                    const storedProofs = localStorage.getItem(SUBMITTED_PROOFS_KEY);
                    if (storedProofs) {
                        submittedProofs = JSON.parse(storedProofs);
                    }
                } catch (e) {
                    console.error("Error တက်သွားသည် :", e);
                    localStorage.removeItem(SUBMITTED_PROOFS_KEY); // Clear corrupted data
                }

                const now = Date.now();
                // Filter out very old hashes during check to keep list clean implicitly
                submittedProofs = submittedProofs.filter(proof => (now - proof.timestamp) < HASH_EXPIRY_MS); 

                const recentDuplicate = submittedProofs.find(
                    proof => proof.hash === currentPaymentProofHash && (now - proof.timestamp) < RECENCY_WINDOW_MS
                );

                if (recentDuplicate) {
                    alert("ထည့်သွင်းထားသော ငွေလွှဲပြေစာ SS သည် မကြာသေးခင်မှ အော်ဒါ တင်ထားသော ပြေစာဖြစ်ပါသည် သင့် GB တိုး အော်ဒါ အား အတည်ပြုပေးရန် ခေတ္တစောင့်ဆိုင်း ပေးပါ။");
                    return;
                }

                if (loadingOverlay) loadingOverlay.style.display = 'flex';
                submitGbPlusButton.disabled = true;
                
                const formData = new FormData();
                formData.append("username", "{{ user.note }}"); 
                formData.append("ordernumber", "{{ user.username }}"); 
                formData.append("amount", selectedPlan.amount);
                formData.append("gb", selectedPlan.gb);
                formData.append("receipt", myDropzone.files[0]); // The actual file
                formData.append("receipt_hash", currentPaymentProofHash); // Send the hash too, n8n might use it

                try {
                    const response = await fetch(n8nWebhookUrl, { method: "POST", body: formData });
                    if (!response.ok) {
                        const errorText = await response.text();
                        alert("Server error: " + response.status + " - " + errorText);
                    } else {
                        alert("GB တိုးမြှင့် ဖောင်တင်ခြင်း အောင်မြင်ပါတယ်။ စစ်ဆေးပြီး Telegram မှအကြောင်းကြားပေးပါမယ်။");
                        
                        // Add hash to localStorage on successful submission
                        submittedProofs.push({ hash: currentPaymentProofHash, timestamp: now });
                        // Manage localStorage size
                        while (submittedProofs.length > MAX_STORED_HASHES) {
                            submittedProofs.shift(); // Remove the oldest
                        }
                        localStorage.setItem(SUBMITTED_PROOFS_KEY, JSON.stringify(submittedProofs));

                        if (myDropzone) myDropzone.removeAllFiles(); // This will trigger removedfile, clearing currentPaymentProofHash
                        planButtons.forEach(btn => btn.classList.remove('selected'));
                        if (totalCostEl) totalCostEl.textContent = '0 ကျပ်';
                        selectedPlan = null;
                        // currentPaymentProofHash is cleared by myDropzone.removeAllFiles() -> removedfile event
                    }
                } catch (error) {
                    console.error('Fetch error:', error);
                    alert("လိုင်းမကောင်းပါ။ အင်တာနက်စစ်ပြီး ထပ်မံကြိုးစားပေးပါ။");
                } finally {
                    if (loadingOverlay) loadingOverlay.style.display = 'none';
                    submitGbPlusButton.disabled = false;
                }
            });
        }
      });
    </script>
</body>
</html>
