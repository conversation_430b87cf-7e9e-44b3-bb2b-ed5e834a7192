{"name": "ipmanweb", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build && sh /app/fix_dirname_error.sh", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check .", "setup": "bash scripts/setup-directories.sh", "migrate": "npx prisma migrate deploy", "migrate-data": "node scripts/migrate-data.js", "test-system": "node scripts/test-system.js", "verify-db": "node scripts/verify-database-consistency.js", "test-admin": "node scripts/test-admin-login.js", "generate-password": "node scripts/generate-admin-password.js", "test-persistence": "node scripts/test-database-persistence.js", "clean-test-records": "node scripts/clean-test-records.js", "migrate-files": "node scripts/migrate-uploaded-files.js", "migrate-db-location": "node scripts/migrate-database-location.js", "db:studio": "npx prisma studio", "db:reset": "npx prisma migrate reset", "db:seed": "npx prisma db seed"}, "devDependencies": {"@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/vite": "^4.0.0", "@types/qrcode": "^1.5.5", "@types/uuid": "^10.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.6.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.0.0", "vite": "^6.2.5", "vite-imagetools": "^7.1.0"}, "dependencies": {"@prisma/client": "^6.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "qrcode": "^1.5.4", "sharp": "^0.34.1", "tailwind-merge": "^3.2.0", "together-ai": "^0.15.0", "uuid": "^11.1.0"}, "prisma": {"seed": "tsx prisma/seed.ts"}}