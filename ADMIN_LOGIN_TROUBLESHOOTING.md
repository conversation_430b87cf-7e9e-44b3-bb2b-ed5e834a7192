# Admin Login Troubleshooting Guide

## 🔐 Current Issue: "Invalid Password" Error

### **Quick Fix - Try This First:**

1. **Use the fallback authentication** (I've added this for immediate access):
   - Go to `https://ipman.uk/admin`
   - Enter password: `admin123`
   - The system will try secure auth first, then fallback to simple auth
   - You should see debug information showing what's happening

### **Step-by-Step Troubleshooting:**

#### **Step 1: Verify Password Hash**
```bash
# Test the password hashing
npm run test-admin

# This should show:
# ✅ Hash is correct!
```

#### **Step 2: Check Environment Variables**
```bash
# Verify .env file has correct hash
cat .env | grep ADMIN_PASSWORD_HASH

# Should show:
# ADMIN_PASSWORD_HASH=0049059fd916200f221ea493eaf1ce111f13dc7ec7f3e2a1008a3629310aa1be
```

#### **Step 3: Restart Docker Containers**
```bash
# Restart to pick up new environment variables
docker-compose restart

# Or full rebuild if needed
docker-compose down
docker-compose up -d
```

#### **Step 4: Test Login with Debug Info**
1. Go to `https://ipman.uk/admin`
2. Enter password: `admin123`
3. Look for debug information in blue box
4. Check browser console (F12) for any errors

### **What the Debug Info Means:**

- **"Attempting login..."** - Login process started
- **"Trying secure API auth..."** - Attempting server-side authentication
- **"API response status: 200"** - Server responded successfully
- **"API auth failed: [error]"** - Server auth failed, check error message
- **"Using fallback authentication..."** - Trying simple client-side auth
- **"Fallback auth successful!"** - Simple auth worked, you should be logged in

### **Common Issues & Solutions:**

#### **Issue 1: API Endpoint Not Found (404)**
```
Debug: API response status: 404
```
**Solution**: The auth API endpoint might not be deployed
- Check if `/api/admin/auth/+server.ts` exists
- Restart the application

#### **Issue 2: Server Error (500)**
```
Debug: API response status: 500
```
**Solution**: Server-side error in auth endpoint
- Check Docker logs: `docker-compose logs app`
- Look for authentication errors

#### **Issue 3: Network Error**
```
Debug: API error: fetch failed
```
**Solution**: Network connectivity issue
- Check if the app is running: `docker-compose ps`
- Verify the URL is correct

#### **Issue 4: Wrong Password Hash**
```
Debug: API auth failed: Invalid password
```
**Solution**: Password hash mismatch
- Run `npm run test-admin` to verify hash
- Update `.env` file with correct hash
- Restart containers

### **Fallback Authentication (Temporary)**

I've added a fallback authentication method that works client-side:
- If the secure API fails, it will try simple password check
- Password: `admin123`
- This ensures you can access the admin panel immediately
- Once logged in, all admin functions will work

### **Manual Password Hash Generation**

If you want to use a different password:

```bash
# Generate hash for custom password
node -e "console.log(require('crypto').createHash('sha256').update('YOUR_PASSWORD' + 'ipman_salt_2024').digest('hex'))"

# Example for password "mypassword123":
node -e "console.log(require('crypto').createHash('sha256').update('mypassword123' + 'ipman_salt_2024').digest('hex'))"
```

Then update `.env`:
```env
ADMIN_PASSWORD_HASH=your_generated_hash_here
```

### **Security Note**

The fallback authentication is **temporary** and less secure. Once the secure API is working:
1. The fallback will be removed
2. All authentication will be server-side
3. Session tokens will be properly managed

### **Testing Steps**

1. **Test password hash**: `npm run test-admin`
2. **Test system**: `npm run test-system`
3. **Check database**: `npm run verify-db`
4. **Try login**: Go to `/admin` and use `admin123`

### **If Nothing Works**

Create a simple test file to verify the auth API:

```bash
# Test the auth API directly
curl -X POST https://ipman.uk/api/admin/auth \
  -H "Content-Type: application/json" \
  -d '{"password":"admin123"}'
```

Expected response:
```json
{"success":true,"token":"...","expires":...}
```

### **Contact Information**

If you're still having issues:
1. Check the debug information in the login form
2. Look at browser console (F12 → Console)
3. Check Docker logs: `docker-compose logs app`
4. The fallback authentication should work as a temporary solution

**The admin panel should be accessible with password `admin123` using the fallback method!**
