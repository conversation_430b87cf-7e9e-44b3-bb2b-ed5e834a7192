#!/usr/bin/env node

/**
 * Clean up test records from persistence testing
 */

import { PrismaClient } from '@prisma/client';

// Use local database path for testing outside Docker
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: 'file:./prisma/dev.db'
    }
  }
});

async function cleanTestRecords() {
  console.log('🧹 Cleaning Test Records\n');

  try {
    // Find all test records
    const testRecords = await prisma.paymentVerification.findMany({
      where: {
        transaction_id: {
          startsWith: 'PERSISTENCE-TEST-'
        }
      }
    });

    console.log(`Found ${testRecords.length} test records to clean`);

    if (testRecords.length === 0) {
      console.log('✅ No test records to clean');
      return;
    }

    // Delete test records
    const deleteResult = await prisma.paymentVerification.deleteMany({
      where: {
        transaction_id: {
          startsWith: 'PERSISTENCE-TEST-'
        }
      }
    });

    console.log(`✅ Deleted ${deleteResult.count} test records`);
    console.log('Database cleaned successfully!');

  } catch (error) {
    console.error('❌ Cleanup failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanTestRecords();
