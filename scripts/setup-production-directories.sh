#!/bin/bash

# Production directory setup script
# This script is run during Docker container startup to ensure all necessary directories exist

echo "Setting up production directories..."

# Create upload directories inside the container
echo "Creating upload directories..."
mkdir -p /app/static/uploads/payment-proofs
chmod 755 /app/static/uploads
chmod 755 /app/static/uploads/payment-proofs

# Ensure proper ownership (if running as appuser)
if [ "$(whoami)" = "appuser" ]; then
    echo "Setting ownership for appuser..."
    # This will only work if the parent directories are already owned by appuser
    # or if we have permission to change ownership
    chown -R appuser:appgroup /app/static/uploads 2>/dev/null || true
fi

echo "Production directories setup complete!"
echo "Upload directory: /app/static/uploads/payment-proofs"

# List the directory to verify it was created
ls -la /app/static/uploads/
