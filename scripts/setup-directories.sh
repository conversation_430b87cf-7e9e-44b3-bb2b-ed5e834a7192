#!/bin/bash

# Setup script to create necessary directories for the application

echo "Setting up directories for IPMan VPN application..."

# Ensure prisma directory exists (should already exist)
echo "Checking prisma directory..."
mkdir -p ./prisma
chmod 755 ./prisma

# Create uploads directory structure
echo "Creating uploads directory structure..."
mkdir -p ./uploads/payment-proofs
chmod 755 ./uploads
chmod 755 ./uploads/payment-proofs

# Create static uploads directory (for development)
echo "Creating static uploads directory..."
mkdir -p ./static/uploads/payment-proofs
chmod 755 ./static/uploads
chmod 755 ./static/uploads/payment-proofs

# Create data directory for database persistence
echo "Creating data directory for database persistence..."
mkdir -p ./data
chmod 755 ./data

# Copy database file to data directory if it doesn't exist
if [ -f "./prisma/dev.db" ] && [ ! -f "./data/dev.db" ]; then
    echo "Copying database to persistent data directory..."
    cp ./prisma/dev.db ./data/dev.db
    chmod 644 ./data/dev.db
    echo "Database copied to data/dev.db"
elif [ ! -f "./data/dev.db" ]; then
    echo "No existing database found. A new one will be created on first run."
fi

# Make scripts executable
echo "Making scripts executable..."
chmod +x ./scripts/*.sh
chmod +x ./scripts/*.js

echo "Directory setup complete!"
echo ""
echo "Directory structure:"
echo "├── data/                    (Persistent database storage - Docker volume)"
echo "├── prisma/                  (Database schema and migrations)"
echo "├── uploads/                 (Production file uploads - Docker volume)"
echo "│   └── payment-proofs/"
echo "├── static/uploads/          (Development file uploads)"
echo "│   └── payment-proofs/"
echo "└── scripts/                 (Utility scripts)"
echo ""
echo "Next steps:"
echo "1. Run 'npm run migrate' to set up the database"
echo "2. Run 'node scripts/migrate-data.js' to migrate existing data"
echo "3. Build and deploy with Docker Compose"
