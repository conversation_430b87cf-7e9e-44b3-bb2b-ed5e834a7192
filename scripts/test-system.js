#!/usr/bin/env node

/**
 * Test script to verify the payment system fixes
 */

const fs = require('fs');
const path = require('path');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...');
  try {
    await prisma.$connect();
    console.log('✅ Database connection successful');
    
    const count = await prisma.paymentVerification.count();
    console.log(`📊 Found ${count} payment verification records`);
    
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

async function testDirectoryStructure() {
  console.log('\n🔍 Testing directory structure...');
  
  const directories = [
    './data',
    './uploads',
    './uploads/payment-proofs',
    './static/uploads',
    './static/uploads/payment-proofs'
  ];
  
  let allGood = true;
  
  for (const dir of directories) {
    if (fs.existsSync(dir)) {
      console.log(`✅ Directory exists: ${dir}`);
    } else {
      console.log(`❌ Directory missing: ${dir}`);
      allGood = false;
    }
  }
  
  return allGood;
}

async function testDatabaseOperations() {
  console.log('\n🔍 Testing database operations...');
  
  try {
    // Test creating a record
    const testTransaction = await prisma.paymentVerification.create({
      data: {
        transaction_id: `TEST-${Date.now()}`,
        order_id: 9999,
        payment_service: 'Test Service',
        verification_status: 'verified'
      }
    });
    console.log('✅ Create operation successful');
    
    // Test finding the record
    const found = await prisma.paymentVerification.findUnique({
      where: { transaction_id: testTransaction.transaction_id }
    });
    
    if (found) {
      console.log('✅ Read operation successful');
    } else {
      console.log('❌ Read operation failed');
      return false;
    }
    
    // Test deleting the record
    await prisma.paymentVerification.delete({
      where: { id: testTransaction.id }
    });
    console.log('✅ Delete operation successful');
    
    return true;
  } catch (error) {
    console.error('❌ Database operations failed:', error.message);
    return false;
  }
}

async function testDuplicateDetection() {
  console.log('\n🔍 Testing duplicate detection...');
  
  try {
    const testId = `DUPLICATE-TEST-${Date.now()}`;
    
    // Create first record
    await prisma.paymentVerification.create({
      data: {
        transaction_id: testId,
        order_id: 8888,
        payment_service: 'Test Service',
        verification_status: 'verified'
      }
    });
    console.log('✅ First record created');
    
    // Try to create duplicate
    try {
      await prisma.paymentVerification.create({
        data: {
          transaction_id: testId,
          order_id: 7777,
          payment_service: 'Test Service',
          verification_status: 'verified'
        }
      });
      console.log('❌ Duplicate detection failed - duplicate was allowed');
      return false;
    } catch (duplicateError) {
      console.log('✅ Duplicate detection working - duplicate was rejected');
    }
    
    // Clean up
    await prisma.paymentVerification.deleteMany({
      where: { transaction_id: testId }
    });
    console.log('✅ Test cleanup completed');
    
    return true;
  } catch (error) {
    console.error('❌ Duplicate detection test failed:', error.message);
    return false;
  }
}

async function testFileUploads() {
  console.log('\n🔍 Testing file upload directories...');
  
  const uploadDirs = [
    './static/uploads/payment-proofs',
    './uploads/payment-proofs'
  ];
  
  let allGood = true;
  
  for (const dir of uploadDirs) {
    try {
      // Test write permissions
      const testFile = path.join(dir, 'test-write.txt');
      fs.writeFileSync(testFile, 'test');
      fs.unlinkSync(testFile);
      console.log(`✅ Write permissions OK: ${dir}`);
    } catch (error) {
      console.log(`❌ Write permissions failed: ${dir} - ${error.message}`);
      allGood = false;
    }
  }
  
  return allGood;
}

async function main() {
  console.log('🚀 Starting Payment System Tests\n');
  
  const tests = [
    { name: 'Directory Structure', fn: testDirectoryStructure },
    { name: 'Database Connection', fn: testDatabaseConnection },
    { name: 'Database Operations', fn: testDatabaseOperations },
    { name: 'Duplicate Detection', fn: testDuplicateDetection },
    { name: 'File Upload Permissions', fn: testFileUploads }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error(`❌ Test "${test.name}" crashed:`, error.message);
      failed++;
    }
  }
  
  console.log('\n📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Payment system is ready.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the issues above.');
  }
  
  await prisma.$disconnect();
  process.exit(failed > 0 ? 1 : 0);
}

main().catch(console.error);
