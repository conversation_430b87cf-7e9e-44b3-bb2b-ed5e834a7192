#!/usr/bin/env node

/**
 * Migration script to move data from JSON file to SQLite database
 * and ensure proper database setup
 */

import fs from 'fs';
import path from 'path';
import { PrismaClient } from '@prisma/client';

// Use local database path for migration outside Docker
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: 'file:./prisma/dev.db'
    }
  }
});

async function main() {
  console.log('Starting data migration...');

  try {
    // Ensure prisma directory exists (it should already exist)
    const prismaDir = path.join(process.cwd(), 'prisma');
    if (!fs.existsSync(prismaDir)) {
      fs.mkdirSync(prismaDir, { recursive: true });
      console.log('Created prisma directory');
    }

    // Check if JSON file exists and has data
    const jsonFilePath = path.join(process.cwd(), 'data', 'transactions.json');
    let jsonData = [];
    
    if (fs.existsSync(jsonFilePath)) {
      try {
        const jsonContent = fs.readFileSync(jsonFilePath, 'utf8');
        jsonData = JSON.parse(jsonContent);
        console.log(`Found ${jsonData.length} records in JSON file`);
      } catch (error) {
        console.log('JSON file exists but is empty or invalid, skipping...');
      }
    } else {
      console.log('No JSON file found, skipping JSON migration...');
    }

    // Check current database records
    const existingRecords = await prisma.paymentVerification.findMany();
    console.log(`Found ${existingRecords.length} existing records in database`);

    // Migrate JSON data to database if needed
    if (jsonData.length > 0) {
      console.log('Migrating JSON data to database...');
      
      for (const record of jsonData) {
        try {
          // Check if record already exists
          const existing = await prisma.paymentVerification.findUnique({
            where: { transaction_id: record.transaction_id }
          });

          if (!existing) {
            await prisma.paymentVerification.create({
              data: {
                transaction_id: record.transaction_id,
                order_id: record.order_id,
                payment_service: record.payment_service || 'Unknown',
                verification_status: record.verification_status || 'verified',
                image_path: record.image_path || null,
                verification_date: record.verification_date ? new Date(record.verification_date) : new Date(),
                created_at: record.created_at ? new Date(record.created_at) : new Date()
              }
            });
            console.log(`Migrated transaction: ${record.transaction_id}`);
          } else {
            console.log(`Transaction already exists: ${record.transaction_id}`);
          }
        } catch (error) {
          console.error(`Error migrating transaction ${record.transaction_id}:`, error.message);
        }
      }

      // Backup the JSON file
      const backupPath = jsonFilePath + '.backup.' + Date.now();
      fs.copyFileSync(jsonFilePath, backupPath);
      console.log(`JSON file backed up to: ${backupPath}`);
    }

    // Final count
    const finalCount = await prisma.paymentVerification.count();
    console.log(`Migration complete. Total records in database: ${finalCount}`);

  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
