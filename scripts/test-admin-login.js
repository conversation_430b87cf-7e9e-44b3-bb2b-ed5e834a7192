#!/usr/bin/env node

/**
 * Test script to verify admin login functionality
 */

import crypto from 'crypto';

function hashPassword(password) {
  return crypto.createHash('sha256').update(password + 'ipman_salt_2024').digest('hex');
}

console.log('🔐 Admin Login Test\n');

// Test password hashing
const testPassword = 'admin123';
const expectedHash = '0049059fd916200f221ea493eaf1ce111f13dc7ec7f3e2a1008a3629310aa1be';
const actualHash = hashPassword(testPassword);

console.log('Password Hash Test:');
console.log(`Input: "${testPassword}"`);
console.log(`Expected: ${expectedHash}`);
console.log(`Actual:   ${actualHash}`);
console.log(`Match: ${expectedHash === actualHash ? '✅ YES' : '❌ NO'}\n`);

if (expectedHash !== actualHash) {
  console.log('❌ Hash mismatch! This explains why login is failing.');
  console.log('🔧 Fix: Update .env file with the correct hash:');
  console.log(`ADMIN_PASSWORD_HASH=${actualHash}`);
} else {
  console.log('✅ Hash is correct!');
}

// Test different passwords
console.log('\nTesting other common passwords:');
const testPasswords = ['admin', 'password', '123456', 'admin123'];

testPasswords.forEach(pwd => {
  const hash = hashPassword(pwd);
  console.log(`"${pwd}" → ${hash}`);
});

console.log('\n📝 Instructions:');
console.log('1. Copy the correct hash to your .env file');
console.log('2. Restart your Docker containers');
console.log('3. Try logging in with "admin123"');
console.log('4. If it still fails, check browser console for errors');

console.log('\n🔧 Generate hash for custom password:');
console.log('node -e "console.log(require(\'crypto\').createHash(\'sha256\').update(\'YOUR_PASSWORD\' + \'ipman_salt_2024\').digest(\'hex\'))"');
