#!/usr/bin/env node

/**
 * Generate admin password hash for .env file
 */

import crypto from 'crypto';
import readline from 'readline';

function hashPassword(password) {
  return crypto.createHash('sha256').update(password + 'ipman_salt_2024').digest('hex');
}

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🔐 Admin Password Hash Generator\n');

rl.question('Enter your desired admin password: ', (password) => {
  if (!password || password.trim().length === 0) {
    console.log('❌ Password cannot be empty!');
    rl.close();
    return;
  }

  const hash = hashPassword(password.trim());
  
  console.log('\n✅ Password hash generated successfully!\n');
  console.log('📋 Add this line to your .env file:');
  console.log(`ADMIN_PASSWORD_HASH=${hash}\n`);
  
  console.log('🔧 Steps to update:');
  console.log('1. Copy the hash above');
  console.log('2. Edit your .env file');
  console.log('3. Replace the ADMIN_PASSWORD_HASH value');
  console.log('4. Restart your Docker containers: docker-compose restart');
  console.log('5. Login with your new password\n');
  
  console.log('⚠️  Security Note:');
  console.log('- Keep this password secure');
  console.log('- Use a strong password for production');
  console.log('- The hash is safe to store in .env file\n');
  
  rl.close();
});
