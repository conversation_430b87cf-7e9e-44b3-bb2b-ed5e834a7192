#!/usr/bin/env node

/**
 * Test database persistence across container restarts
 */

import { PrismaClient } from '@prisma/client';

// Use the DATABASE_URL from environment variables (.env file)
// This ensures consistency between test and production
const prisma = new PrismaClient();

async function testPersistence() {
  console.log('🧪 Testing Database Persistence\n');

  try {
    // 1. Check current database location
    console.log('📍 Database Configuration:');
    console.log(`DATABASE_URL: ${process.env.DATABASE_URL}`);
    
    // 2. Count existing records
    const existingCount = await prisma.paymentVerification.count();
    console.log(`📊 Existing records: ${existingCount}`);

    // 3. Create a test record with timestamp
    const testId = `PERSISTENCE-TEST-${Date.now()}`;
    console.log(`\n🔬 Creating test record: ${testId}`);
    
    const testRecord = await prisma.paymentVerification.create({
      data: {
        transaction_id: testId,
        order_id: 99999,
        payment_service: 'Persistence Test',
        verification_status: 'verified'
      }
    });
    
    console.log(`✅ Test record created with ID: ${testRecord.id}`);

    // 4. Verify it was saved
    const savedRecord = await prisma.paymentVerification.findUnique({
      where: { transaction_id: testId }
    });
    
    if (savedRecord) {
      console.log(`✅ Test record verified in database`);
      console.log(`   - ID: ${savedRecord.id}`);
      console.log(`   - Transaction ID: ${savedRecord.transaction_id}`);
      console.log(`   - Created: ${savedRecord.created_at}`);
    } else {
      console.log(`❌ Test record not found!`);
      return;
    }

    // 5. Instructions for testing persistence
    console.log(`\n📋 Persistence Test Instructions:`);
    console.log(`1. Note the test record ID: ${testRecord.id}`);
    console.log(`2. Restart your Docker containers:`);
    console.log(`   docker-compose restart`);
    console.log(`3. Run this script again:`);
    console.log(`   npm run test-persistence`);
    console.log(`4. The test record should still exist!`);

    // 6. Check for previous test records
    const allTestRecords = await prisma.paymentVerification.findMany({
      where: {
        transaction_id: {
          startsWith: 'PERSISTENCE-TEST-'
        }
      },
      orderBy: { created_at: 'desc' }
    });

    if (allTestRecords.length > 1) {
      console.log(`\n🎉 PERSISTENCE CONFIRMED!`);
      console.log(`Found ${allTestRecords.length} test records from previous runs:`);
      allTestRecords.forEach((record, index) => {
        console.log(`   ${index + 1}. ID ${record.id} - ${record.transaction_id} (${record.created_at})`);
      });
      console.log(`This proves the database persists across container restarts! ✅`);
    } else {
      console.log(`\n⏳ First test run. Restart containers and run again to confirm persistence.`);
    }

    // 7. Cleanup option
    console.log(`\n🧹 Cleanup:`);
    console.log(`To remove test records, run:`);
    console.log(`npm run clean-test-records`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testPersistence();
