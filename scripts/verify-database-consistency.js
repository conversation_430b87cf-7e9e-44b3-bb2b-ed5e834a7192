#!/usr/bin/env node

/**
 * Database consistency verification script
 * Checks for data inconsistencies and ensures single source of truth
 */

const fs = require('fs');
const path = require('path');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('🔍 Verifying Database Consistency...\n');

  try {
    // 1. Check SQLite Database (Primary Source)
    console.log('📊 Checking SQLite Database (Primary Source)...');
    const sqliteRecords = await prisma.paymentVerification.findMany({
      orderBy: { created_at: 'desc' }
    });
    console.log(`✅ SQLite Database: ${sqliteRecords.length} records found`);
    
    // 2. Check JSON File (Legacy)
    console.log('\n📄 Checking JSON File (Legacy)...');
    const jsonFilePath = path.join(process.cwd(), 'data', 'transactions.json');
    let jsonRecords = [];
    
    if (fs.existsSync(jsonFilePath)) {
      try {
        const jsonContent = fs.readFileSync(jsonFilePath, 'utf8');
        jsonRecords = JSON.parse(jsonContent);
        console.log(`⚠️  JSON File: ${jsonRecords.length} records found (LEGACY - should be migrated)`);
      } catch (error) {
        console.log('❌ JSON File: Invalid or corrupted');
      }
    } else {
      console.log('✅ JSON File: Not found (good - using SQLite only)');
    }

    // 3. Compare Data Sources
    console.log('\n🔄 Comparing Data Sources...');
    
    if (jsonRecords.length > 0) {
      console.log('⚠️  WARNING: JSON file still contains data!');
      console.log('   This could cause inconsistencies.');
      console.log('   Recommendation: Run migration script to move data to SQLite');
      
      // Check for records in JSON but not in SQLite
      const sqliteTransactionIds = new Set(sqliteRecords.map(r => r.transaction_id));
      const jsonOnlyRecords = jsonRecords.filter(r => !sqliteTransactionIds.has(r.transaction_id));
      
      if (jsonOnlyRecords.length > 0) {
        console.log(`❌ INCONSISTENCY: ${jsonOnlyRecords.length} records exist in JSON but not in SQLite:`);
        jsonOnlyRecords.forEach(record => {
          console.log(`   - Transaction ID: ${record.transaction_id}, Order ID: ${record.order_id}`);
        });
      }
    }

    // 4. Verify Database Integrity
    console.log('\n🔒 Verifying Database Integrity...');
    
    // Check for duplicate transaction IDs (should be impossible due to unique constraint)
    const duplicateCheck = await prisma.$queryRaw`
      SELECT transaction_id, COUNT(*) as count 
      FROM PaymentVerification 
      GROUP BY transaction_id 
      HAVING COUNT(*) > 1
    `;
    
    if (duplicateCheck.length > 0) {
      console.log('❌ CRITICAL: Duplicate transaction IDs found in database!');
      duplicateCheck.forEach(dup => {
        console.log(`   - Transaction ID: ${dup.transaction_id} appears ${dup.count} times`);
      });
    } else {
      console.log('✅ No duplicate transaction IDs found');
    }

    // 5. Check Database File Location
    console.log('\n📁 Checking Database File Location...');
    const dbPath = process.env.DATABASE_URL?.replace('file:', '') || '/app/prisma/dev.db';
    console.log(`Database URL: ${process.env.DATABASE_URL}`);
    console.log(`Database Path: ${dbPath}`);
    
    // Check if database file exists
    try {
      const stats = fs.statSync(dbPath);
      console.log(`✅ Database file exists: ${dbPath}`);
      console.log(`   Size: ${(stats.size / 1024).toFixed(2)} KB`);
      console.log(`   Modified: ${stats.mtime}`);
    } catch (error) {
      console.log(`❌ Database file not found: ${dbPath}`);
      console.log('   This could indicate a configuration issue');
    }

    // 6. Test Database Operations
    console.log('\n🧪 Testing Database Operations...');
    
    // Test read operation
    try {
      const testRead = await prisma.paymentVerification.findFirst();
      console.log('✅ Database read operation successful');
    } catch (error) {
      console.log('❌ Database read operation failed:', error.message);
    }

    // Test write operation (create and delete a test record)
    try {
      const testRecord = await prisma.paymentVerification.create({
        data: {
          transaction_id: `CONSISTENCY-TEST-${Date.now()}`,
          order_id: 99999,
          payment_service: 'Test Service',
          verification_status: 'verified'
        }
      });
      
      await prisma.paymentVerification.delete({
        where: { id: testRecord.id }
      });
      
      console.log('✅ Database write operation successful');
    } catch (error) {
      console.log('❌ Database write operation failed:', error.message);
    }

    // 7. Summary and Recommendations
    console.log('\n📋 Summary and Recommendations:');
    console.log(`✅ Primary Database (SQLite): ${sqliteRecords.length} records`);
    
    if (jsonRecords.length > 0) {
      console.log(`⚠️  Legacy JSON File: ${jsonRecords.length} records (NEEDS MIGRATION)`);
      console.log('   🔧 Action Required: Run "npm run migrate-data" to migrate JSON data');
    }
    
    console.log('\n🎯 Data Flow Verification:');
    console.log('   1. OpenRouter AI → SQLite Database ✅');
    console.log('   2. Admin Dashboard → SQLite Database ✅');
    console.log('   3. Duplicate Check → SQLite Database ✅');
    
    if (jsonRecords.length === 0) {
      console.log('\n🎉 CONSISTENCY STATUS: GOOD');
      console.log('   All data is stored in SQLite database');
      console.log('   No legacy JSON files interfering');
    } else {
      console.log('\n⚠️  CONSISTENCY STATUS: NEEDS ATTENTION');
      console.log('   Legacy JSON data needs migration');
    }

  } catch (error) {
    console.error('❌ Consistency check failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
