#!/usr/bin/env node

/**
 * Migrate uploaded files from static/uploads to the proper upload directory
 * This script helps move existing files to the correct location for production
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.dirname(__dirname);

// Source and destination directories
const sourceDir = path.join(projectRoot, 'static', 'uploads', 'payment-proofs');
const destDir = path.join(projectRoot, 'uploads', 'payment-proofs');

async function migrateFiles() {
  console.log('🔄 Starting file migration...');
  console.log(`Source: ${sourceDir}`);
  console.log(`Destination: ${destDir}`);

  try {
    // Check if source directory exists
    try {
      await fs.access(sourceDir);
    } catch {
      console.log('❌ Source directory does not exist. Nothing to migrate.');
      return;
    }

    // Create destination directory if it doesn't exist
    await fs.mkdir(destDir, { recursive: true });
    console.log('✅ Destination directory created/verified');

    // Read files from source directory
    const files = await fs.readdir(sourceDir);
    console.log(`📁 Found ${files.length} files to migrate`);

    if (files.length === 0) {
      console.log('✅ No files to migrate');
      return;
    }

    let migratedCount = 0;
    let skippedCount = 0;

    for (const file of files) {
      const sourcePath = path.join(sourceDir, file);
      const destPath = path.join(destDir, file);

      try {
        // Check if file is actually a file (not a directory)
        const stat = await fs.stat(sourcePath);
        if (!stat.isFile()) {
          console.log(`⏭️  Skipping ${file} (not a file)`);
          skippedCount++;
          continue;
        }

        // Check if destination file already exists
        try {
          await fs.access(destPath);
          console.log(`⏭️  Skipping ${file} (already exists in destination)`);
          skippedCount++;
          continue;
        } catch {
          // File doesn't exist in destination, proceed with copy
        }

        // Copy file to destination
        await fs.copyFile(sourcePath, destPath);
        console.log(`✅ Migrated: ${file}`);
        migratedCount++;

      } catch (error) {
        console.error(`❌ Error migrating ${file}:`, error.message);
      }
    }

    console.log('\n📊 Migration Summary:');
    console.log(`✅ Successfully migrated: ${migratedCount} files`);
    console.log(`⏭️  Skipped: ${skippedCount} files`);
    console.log(`📁 Total processed: ${files.length} files`);

    if (migratedCount > 0) {
      console.log('\n🎉 Migration completed successfully!');
      console.log('\n📝 Next steps:');
      console.log('1. Verify files in the uploads directory');
      console.log('2. Test file access through the web interface');
      console.log('3. Consider removing files from static/uploads after verification');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
migrateFiles().catch(console.error);
