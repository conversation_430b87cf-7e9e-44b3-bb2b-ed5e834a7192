#!/bin/bash

# This script cleans up payment proof images older than 1 day
# It should be run daily via cron

# Get the directory of the script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Go to the project root directory
cd "$SCRIPT_DIR/.."

# Get the server URL from environment or use default
SERVER_URL=${SERVER_URL:-"http://localhost:5173"}

# Call the cleanup endpoint
echo "Cleaning up payment proof images..."
curl -s "$SERVER_URL/api/cleanup-payment-proofs"

echo "Cleanup completed."
