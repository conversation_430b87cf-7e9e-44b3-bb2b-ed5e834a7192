#!/usr/bin/env node

/**
 * Migrate data from old database location to new location
 */

import { PrismaClient } from '@prisma/client';

// Old database location
const oldPrisma = new PrismaClient({
  datasources: {
    db: {
      url: 'file:./prisma/dev.db'
    }
  }
});

// New database location (uses .env DATABASE_URL)
const newPrisma = new PrismaClient();

async function migrateData() {
  console.log('🔄 Migrating Database Location\n');

  try {
    // 1. Check old database
    console.log('📊 Checking old database (./prisma/dev.db)...');
    const oldRecords = await oldPrisma.paymentVerification.findMany({
      orderBy: { created_at: 'asc' }
    });
    console.log(`✅ Found ${oldRecords.length} records in old database`);

    if (oldRecords.length === 0) {
      console.log('ℹ️  No data to migrate');
      return;
    }

    // 2. Check new database
    console.log('\n📊 Checking new database (./data/dev.db)...');
    const newRecords = await newPrisma.paymentVerification.findMany();
    console.log(`✅ Found ${newRecords.length} records in new database`);

    // 3. Migrate data
    console.log('\n🔄 Migrating records...');
    let migratedCount = 0;
    let skippedCount = 0;

    for (const record of oldRecords) {
      try {
        // Check if record already exists in new database
        const existing = await newPrisma.paymentVerification.findUnique({
          where: { transaction_id: record.transaction_id }
        });

        if (existing) {
          console.log(`⏭️  Skipping existing record: ${record.transaction_id}`);
          skippedCount++;
          continue;
        }

        // Create record in new database
        await newPrisma.paymentVerification.create({
          data: {
            transaction_id: record.transaction_id,
            order_id: record.order_id,
            payment_service: record.payment_service,
            verification_date: record.verification_date,
            image_path: record.image_path,
            verification_status: record.verification_status,
            created_at: record.created_at,
            updated_at: record.updated_at
          }
        });

        console.log(`✅ Migrated: ${record.transaction_id}`);
        migratedCount++;

      } catch (error) {
        console.error(`❌ Failed to migrate ${record.transaction_id}:`, error.message);
      }
    }

    // 4. Verify migration
    console.log('\n🔍 Verifying migration...');
    const finalCount = await newPrisma.paymentVerification.count();
    console.log(`✅ New database now has ${finalCount} records`);

    console.log('\n📊 Migration Summary:');
    console.log(`   - Records migrated: ${migratedCount}`);
    console.log(`   - Records skipped: ${skippedCount}`);
    console.log(`   - Total in new DB: ${finalCount}`);

    if (migratedCount > 0) {
      console.log('\n🎉 Migration completed successfully!');
      console.log('\n📋 Next Steps:');
      console.log('1. Test the application with the new database location');
      console.log('2. If everything works, you can remove the old database files:');
      console.log('   - rm prisma/dev.db');
      console.log('   - rm prisma/prisma/dev.db');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await oldPrisma.$disconnect();
    await newPrisma.$disconnect();
  }
}

migrateData();
