# Quick Deployment Guide

## 🚀 Deploy the Fixed Payment System

### Prerequisites
- Docker and Docker Compose installed
- Access to your server/CapRover instance
- Git repository access

### Step 1: Prepare the Environment

```bash
# 1. Setup directories and permissions
npm run setup

# 2. Test the system (optional but recommended)
npm run test-system
```

### Step 2: Database Setup

```bash
# 1. Run database migrations
npm run migrate

# 2. Migrate existing JSON data (if you have any)
npm run migrate-data

# 3. Verify database setup
npm run db:studio  # Opens Prisma Studio in browser
```

### Step 3: Build and Deploy

```bash
# 1. Build the Docker image
docker-compose build

# 2. Deploy with persistent volumes
docker-compose up -d

# 3. Check logs
docker-compose logs -f app
```

### Step 4: Verify the Fixes

#### Test Image Access
1. Upload a payment proof image through your app
2. Note the returned URL (e.g., `https://ipman.uk/uploads/payment-proofs/payment-proof-xxx.png`)
3. Access the URL directly in browser - should display the image
4. Restart containers: `docker-compose restart`
5. Access the URL again - should still work ✅

#### Test Transaction Persistence
1. Upload a payment with a unique transaction ID
2. Restart containers: `docker-compose restart`
3. Try uploading the same transaction ID again
4. Should be rejected as duplicate ✅

#### Test Admin Interface
1. Navigate to `https://ipman.uk/admin`
2. Login with password: `admin123`
3. View payment verifications
4. Test search and delete functions ✅

### Step 5: Production Configuration

#### Change Admin Password
Edit `src/routes/admin/+layout.svelte`:
```javascript
const ADMIN_PASSWORD = 'your-secure-password-here';
```

#### Update Environment Variables
Ensure your `.env` file has:
```env
DATABASE_URL="file:/app/data/dev.db"
YOUR_DOMAIN=ipman.uk
OPENROUTER_API_KEY=your-api-key
```

### Step 6: Monitoring

#### Check System Health
```bash
# View logs
docker-compose logs -f

# Check container status
docker-compose ps

# Monitor disk usage
df -h

# Check database
npm run db:studio
```

#### Admin Dashboard
- Access: `https://ipman.uk/admin`
- Monitor payment verifications
- View system statistics
- Manage duplicate transactions

### Troubleshooting

#### Images Not Loading
```bash
# Check volume mounts
docker-compose exec app ls -la /app/static/uploads/payment-proofs/

# Check permissions
docker-compose exec app ls -la /app/static/uploads/

# Restart Caddy
docker-compose restart caddy
```

#### Database Issues
```bash
# Check database file
docker-compose exec app ls -la /app/data/

# Test database connection
docker-compose exec app npx prisma db push

# View database
npm run db:studio
```

#### Admin Access Issues
```bash
# Check admin routes
curl -I https://ipman.uk/admin

# Check browser console for errors
# Verify password in layout file
```

### Backup Strategy

#### Database Backup
```bash
# Copy database file
docker cp $(docker-compose ps -q app):/app/data/dev.db ./backup-$(date +%Y%m%d).db
```

#### Files Backup
```bash
# Backup uploads
tar -czf uploads-backup-$(date +%Y%m%d).tar.gz ./uploads/
```

### Performance Tips

1. **Regular Cleanup**: The cron job automatically cleans old payment proofs
2. **Database Optimization**: Consider periodic VACUUM for SQLite
3. **Monitoring**: Set up alerts for disk space and container health
4. **Scaling**: For high traffic, consider PostgreSQL instead of SQLite

### Security Checklist

- [ ] Changed default admin password
- [ ] Enabled HTTPS (handled by Caddy)
- [ ] Regular backups configured
- [ ] File upload limits configured
- [ ] Database access restricted
- [ ] Admin routes protected

### Next Steps After Deployment

1. **Test thoroughly** with real payment uploads
2. **Monitor logs** for any errors
3. **Set up automated backups**
4. **Document any custom configurations**
5. **Train team on admin interface usage**

---

## 🎉 Success!

Your payment system should now:
- ✅ Serve uploaded images correctly
- ✅ Persist transaction data across restarts
- ✅ Prevent duplicate payments
- ✅ Provide admin interface for management

Access your admin panel at: `https://ipman.uk/admin`
