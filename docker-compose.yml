services:
  app:
    build: .
    restart: unless-stopped
    env_file: .env # Load environment variables from .env file
    environment:
      - PORT=3000 # Ensure the app listens on this port inside the container
      - DATABASE_URL=file:/app/data/dev.db # Override for Docker container path
      # Add other necessary runtime environment variables here if not in .env
    volumes:
      # Mount a volume for persistent uploads
      # Maps host ./uploads directory to /app/static/uploads inside the container
      # This allows access to all uploaded files including payment-proofs
      - ./uploads:/app/static/uploads
      # Mount the entire data directory for database persistence
      # This ensures the database directory structure is maintained across rebuilds
      - ./data:/app/data
    # depends_on: # Commented out as SQLite doesn't need a separate DB service
      # Add database service dependency if using a separate DB container (e.g., postgres)
      # - db # Example: Uncomment and configure if using a separate DB service

  caddy:
    image: caddy:latest
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "443:443/udp" # Required for HTTP/3
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile # Mount the Caddy configuration file
      - caddy_data:/data # Persist Caddy's state (certificates, etc.)
      - caddy_config:/config # Persist Caddy's configuration
    environment:
      # Pass environment variables needed by Caddyfile (e.g., for basicauth)
      - CADDY_ADMIN_USER=${CADDY_ADMIN_USER}
      - CADDY_ADMIN_PASSWORD_HASH=${CADDY_ADMIN_PASSWORD_HASH}
      - YOUR_DOMAIN=${YOUR_DOMAIN} # Optional: If used in Caddyfile
    depends_on:
      - app

  # Example Database Service (Uncomment and configure if needed)
  # db:
  #   image: postgres:15-alpine # Or your preferred database image
  #   restart: unless-stopped
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   environment:
  #     - POSTGRES_DB=${POSTGRES_DB}
  #     - POSTGRES_USER=${POSTGRES_USER}
  #     - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}

volumes:
  caddy_data: {}
  caddy_config: {}
  # postgres_data: {} # Uncomment if using a separate DB service
