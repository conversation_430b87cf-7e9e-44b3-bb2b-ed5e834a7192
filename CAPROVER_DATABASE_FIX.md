# Caprover Database Persistence & Order ID Fix Guide

## Problem 1: Database Wiped on Each Deployment

### Issue
When deploying to Caprover via GitHub Actions, the database is wiped because:
- The `docker-compose.yml` volume mounts are only used locally
- Caprover doesn't use docker-compose.yml - it uses the Dockerfile directly
- The database directory `/app/data` is inside the container and gets recreated on each deployment

### Solution
Configure persistent volumes in Caprover:

1. **Login to Caprover Dashboard**
2. **Navigate to your app**
3. **Go to "App Configs" tab**
4. **Add Persistent Directories:**
   ```
   Path in App: /app/data
   Label: database-storage
   
   Path in App: /app/static/uploads
   Label: uploads-storage
   ```
5. **Click "Save & Update"**
6. **Restart the app**

### Alternative: Using Caprover's captain-definition
Create a `captain-definition` file in your project root:
```json
{
  "schemaVersion": 2,
  "dockerfileLines": [
    "FROM ghcr.io/yourusername/ipmanweb:main"
  ],
  "volumes": [
    {
      "containerPath": "/app/data",
      "volumeName": "ipman-database"
    },
    {
      "containerPath": "/app/static/uploads", 
      "volumeName": "ipman-uploads"
    }
  ]
}
```

## Problem 2: Wrong Order ID in Payment Verification

### Issue
The payment verification is saving the product ID instead of the order ID because:
1. Payment verification happens BEFORE the order is created
2. The code passes `product.id` to the verification function
3. The actual WooCommerce order ID is only available AFTER order creation

### Current Flow (Wrong):
```
1. User uploads payment → 2. Verify with product.id → 3. Create order → 4. Get real order.id
```

### Solution Options:

#### Option A: Two-Phase Verification (Recommended)
1. Create order first with "pending-verification" status
2. Verify payment with the real order ID
3. Update order status based on verification

#### Option B: Update Verification Record After Order Creation
1. Keep current flow but return verification record ID
2. After order creation, update the verification record with correct order ID

### Implementation for Option B (Minimal Changes):

1. **Update Payment Verification API** to return the verification record ID
2. **Update Database Schema** to allow updating order_id
3. **Add Update Endpoint** to update verification record with correct order ID
4. **Update Frontend** to call update endpoint after order creation

## Quick Fix Implementation

### Step 1: Add Update Function to db.ts ✅
```typescript
// Add to src/lib/server/db.ts
export const updateVerificationOrderId = async (
  verificationId: number,
  orderId: number
): Promise<boolean> => {
  try {
    await prisma.paymentVerification.update({
      where: { id: verificationId },
      data: { order_id: orderId }
    });
    console.log(`Updated verification ${verificationId} with order ID ${orderId}`);
    return true;
  } catch (error) {
    console.error(`Error updating verification order ID:`, error);
    return false;
  }
};
```

### Step 2: Return Verification ID from API ✅
Updated `src/routes/api/payment-verification/verify/+server.ts` to return `verificationId` in the success response.

### Step 3: Create Update Endpoint ✅
Created `src/routes/api/payment-verification/update-order-id/+server.ts` to update verification records.

### Step 4: Update Frontend Components ✅
Updated both `CheckoutForm.svelte` and `BandwidthUpgradeModal.svelte` to:
1. Store the verification ID from the verification response
2. After order creation, call the update endpoint with the real order ID

## Implementation Summary

The fix works by:
1. **During payment verification**: Save with temporary ID (product ID)
2. **After order creation**: Update the verification record with the real WooCommerce order ID
3. **Result**: Payment verifications now have the correct order IDs

### Files Modified:
- `src/lib/server/db.ts` - Added `updateVerificationOrderId` function
- `src/routes/api/payment-verification/verify/+server.ts` - Returns verification ID
- `src/lib/types.ts` - Added `verificationId` to interface
- `src/routes/api/payment-verification/update-order-id/+server.ts` - New endpoint
- `src/lib/components/CheckoutForm.svelte` - Updates verification after order
- `src/lib/components/BandwidthUpgradeModal.svelte` - Updates verification after order

## Deployment Checklist

1. **For Database Persistence:**
   - [ ] Configure persistent volumes in Caprover dashboard
   - [ ] Test by deploying and checking if data persists
   - [ ] Verify uploads directory also persists

2. **For Order ID Fix:**
   - [ ] Deploy code changes
   - [ ] Test new order creation
   - [ ] Verify correct order IDs in payment_verifications table

## Testing Commands

```bash
# SSH into your Caprover server
docker exec -it $(docker ps -q -f name=your-app-name) sh

# Check database
cd /app/data
ls -la

# Check payment verifications
npx prisma studio
```

## Important Notes

1. **Backup your database** before making changes
2. **Test in staging** if possible
3. **Monitor first few deployments** after implementing fixes
4. The volume configuration in Caprover is **critical** - without it, data will be lost on each deployment 