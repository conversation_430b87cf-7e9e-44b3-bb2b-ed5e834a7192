# IPman VPN Website

This is the IPman VPN website built with SvelteKit and integrated with WooCommerce as a headless CMS.

## Features

- Display VPN plans from WooCommerce
- Direct checkout without shopping cart
- Payment proof upload
- Responsive design

## Setup

### Prerequisites

- Node.js (v18 or later)
- npm or yarn
- WordPress with WooCommerce installed

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file in the root directory with the following variables:

```
VITE_WOOCOMMERCE_URL=https://your-wordpress-site.com
VITE_WOOCOMMERCE_CONSUMER_KEY=your_consumer_key
VITE_WOOCOMMERCE_CONSUMER_SECRET=your_consumer_secret
```

4. Start the development server:

```bash
npm run dev
```

### WordPress Plugin Installation

1. Upload the `wordpress-plugin` folder to your WordPress site's `wp-content/plugins` directory
2. Activate the "IPman Payment Uploads" plugin in the WordPress admin panel
3. Make sure your WooCommerce API keys have read/write permissions

## WooCommerce API Setup

1. Go to WooCommerce > Settings > Advanced > REST API
2. Click "Add key"
3. Enter a description (e.g., "IPman VPN Website")
4. Set User to an admin account
5. Set Permissions to "Read/Write"
6. Click "Generate API key"
7. Copy the Consumer Key and Consumer Secret to your `.env` file

## Checkout Process

The checkout process is streamlined for simplicity:

1. User selects a plan and its variation (if applicable)
2. User clicks "Buy Now" button
3. User fills out the checkout form with name and phone number
4. User uploads payment proof
5. Order is created in WooCommerce with "Processing" status
6. Payment proof is attached to the order

## Development

- `src/routes/plans` - Main product listing page
- `src/routes/plans/[id]` - Product detail page
- `src/lib/components/CheckoutForm.svelte` - Checkout form component
- `src/lib/components/OrderConfirmation.svelte` - Order confirmation component
- `src/lib/services/woocommerce.ts` - WooCommerce API service

## Building for Production

```bash
npm run build
```

Preview the production build:

```bash
npm run preview
```

## Deployment with CapRover and GitHub Actions

This project is configured for automated deployment to a VPS using CapRover, with a CI/CD pipeline managed by GitHub Actions.

### Prerequisites for Deployment

1.  **VPS Server:** A Virtual Private Server (e.g., from DigitalOcean, Linode, AWS EC2, etc.) with a clean OS (Ubuntu recommended).
2.  **Domain Name:** A registered domain name that you can manage DNS records for.
3.  **GitHub Repository:** Your project code hosted on GitHub.

### I. CapRover Server Setup

1.  **Install CapRover on your VPS:**
    *   SSH into your VPS.
    *   Follow the official CapRover installation guide: [https://caprover.com/docs/get-started.html](https://caprover.com/docs/get-started.html)
    *   Typically, this involves running a Docker command:
        ```bash
        docker run -p 80:80 -p 443:443 -p 3000:3000 -v /var/run/docker.sock:/var/run/docker.sock -v /captain:/captain caprover/caprover
        ```
    *   During setup, CapRover will ask for a root domain (e.g., `captain.yourdomain.com`). Point an `A` record for this subdomain to your VPS IP address.
    *   Access your CapRover dashboard via `http://captain.yourdomain.com` (it will redirect to HTTPS automatically after initial setup) and complete the initial login and password setup.

### II. Application Setup in CapRover

1.  **Login to CapRover Dashboard:** Access `https://captain.yourdomain.com`.
2.  **Create a New App:**
    *   Go to "Apps" and click "Create New App".
    *   Choose an "App Name" (e.g., `ipman-web`). This name will be used in GitHub Actions secrets.
    *   Select "Has Persistent Data" if you need to store uploaded payment proofs or other data directly on the server filesystem across deployments.
        *   If selected, define a "Persistent Directory" (e.g., `/usr/src/app/static/uploads` if your Dockerfile places uploads there and you want them to persist).
3.  **Configure App Domain:**
    *   In your app's settings within CapRover, go to the "HTTP Settings" tab.
    *   Add your desired application domain (e.g., `yourdomain.com` or `app.yourdomain.com`).
    *   Point an `A` record for this domain/subdomain to your VPS IP address.
    *   Enable "Enable HTTPS" in CapRover for this domain. CapRover will handle SSL certificate provisioning (usually via Let's Encrypt).
4.  **Environment Variables:**
    *   Go to your app's "App Configs" tab.
    *   Add all necessary environment variables required by your SvelteKit application at runtime. These include:
        *   `DATABASE_URL`: The connection string for your Prisma database.
        *   `VITE_WOOCOMMERCE_URL`
        *   `VITE_WOOCOMMERCE_CONSUMER_KEY`
        *   `VITE_WOOCOMMERCE_CONSUMER_SECRET`
        *   Any other API keys or secrets.
    *   **Important:** Do not commit sensitive `.env` files. Use CapRover's environment variable management.
5.  **Deployment Token:**
    *   Go to your app's "Deployment" tab.
    *   Under "Method 3: Deploy via ImageName", click "Enable App Token".
    *   Copy the generated "App Token". This will be used as a GitHub secret.

### III. GitHub Actions Setup

1.  **Repository Secrets:**
    *   In your GitHub repository, go to "Settings" > "Secrets and variables" > "Actions".
    *   Click "New repository secret" for each of the following:
        *   `CAPROVER_SERVER`: The full URL to your CapRover dashboard (e.g., `https://captain.yourdomain.com`).
        *   `CAPROVER_APP_NAME`: The name you gave your app in CapRover (e.g., `ipman-web`).
        *   `CAPROVER_APP_TOKEN`: The app token you copied from CapRover's deployment settings.
        *   (Optional) `GHCR_TOKEN`: If you choose not to use the default `GITHUB_TOKEN` for pushing to GitHub Container Registry, provide a Personal Access Token (PAT) with `write:packages` scope. The current workflow uses `GITHUB_TOKEN`.

### IV. Deployment Workflow

The deployment workflow is defined in `.github/workflows/deploy.yml`.

1.  **Triggering Deployment:**
    *   Pushing changes to the `main` branch will automatically trigger the workflow.
    *   You can also manually trigger the workflow from the "Actions" tab in your GitHub repository.
2.  **Workflow Steps:**
    *   Checks out the code.
    *   Sets up Node.js and Docker Buildx.
    *   Builds the Docker image using the `Dockerfile` in the project root.
        *   The `Dockerfile` is configured to:
            *   Run the application as a non-root user.
            *   Install `supercronic` to run scheduled tasks (defined in `cleanup.crontab`).
            *   Run `prisma migrate deploy` on container startup.
            *   Set the container timezone (default `Asia/Rangoon`, configurable via `TZ` build arg).
    *   Pushes the built image to GitHub Container Registry (GHCR).
    *   Uses the `caprover/deploy-from-github` action to instruct your CapRover instance to pull the new image from GHCR and deploy it to your application.

### V. Database Setup

*   **Prisma Migrations:** The `Dockerfile` is configured to run `npx prisma migrate deploy` automatically when the container starts. This applies any pending database migrations.
*   **Database Hosting:**
    *   **CapRover One-Click App:** You can deploy a database (e.g., PostgreSQL, MySQL) as a one-click app within CapRover and connect your SvelteKit application to it using the internal service name or by exposing the database port.
    *   **External Managed Database:** Alternatively, use an external managed database service (e.g., AWS RDS, DigitalOcean Managed Databases, Supabase).
    *   Ensure the `DATABASE_URL` environment variable in your CapRover app configuration points to your chosen database.

### VI. Managing the Cleanup Script

*   The `scripts/cleanup-payment-proofs.sh` script is scheduled to run daily at 2 AM (container time) via `supercronic`.
*   The schedule is defined in `cleanup.crontab`.
*   The container's timezone can be set via the `TZ` build argument in the `Dockerfile` (defaults to `Asia/Rangoon`).

### Troubleshooting

*   **Check GitHub Actions Logs:** If a deployment fails, the first place to look is the output of the GitHub Actions workflow run.
*   **Check CapRover App Logs:** In your CapRover dashboard, view the logs for your application to see runtime errors or issues during container startup (e.g., problems with Prisma migrations, environment variables).
*   **CapRover NetData:** CapRover includes NetData for monitoring server and application resource usage, which can be helpful for diagnosing performance issues. Access it via `https://captain.yourdomain.com` and navigate to the "Monitoring" section.
