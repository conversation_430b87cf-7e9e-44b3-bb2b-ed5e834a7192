# 🔍 Debug Upload Issue - Production 404 Errors

## 🚨 **Current Problem**
Newly uploaded files via CheckoutForm return 404 errors on production server:
- URL: `/uploads/payment-proofs/photo_2025-05-06_13-21-59.jpg`
- Status: 404 Not Found

## 🔧 **Debugging Changes Added**

### **1. Upload API Debugging (`src/routes/api/upload-payment-proof/+server.ts`)**
Added detailed logging to show:
- Environment variables (`ENV_UPLOAD_DIR`, `uploadDirPath`)
- Original filename vs generated unique filename
- Actual save path
- File save success/failure

### **2. File Serving Route Debugging (`src/routes/uploads/[...path]/+server.ts`)**
Added detailed logging to show:
- Requested file path
- Environment variables
- Constructed full path
- File existence check

## 🎯 **Expected Debug Output**

### **During Upload:**
```
Upload details: {
  originalName: "photo_2025-05-06_13-21-59.jpg",
  uniqueFilename: "payment-proof-abc123-def456.jpg",
  type: "image/jpeg",
  size: 123456,
  savedPath: "/app/static/uploads/payment-proofs/payment-proof-abc123-def456.jpg",
  env_UPLOAD_DIR: "/app/static/uploads/payment-proofs",
  uploadDirPath: "/app/static/uploads/payment-proofs"
}
```

### **During File Access:**
```
File serving request: {
  filePath: "payment-proofs/payment-proof-abc123-def456.jpg",
  UPLOAD_DIR: "/app/static/uploads",
  env_UPLOAD_DIR: "/app/static/uploads/payment-proofs"
}

Attempting to serve file: {
  fullPath: "/app/static/uploads/payment-proofs/payment-proof-abc123-def456.jpg",
  exists: true/false
}
```

## 🔍 **Key Questions to Answer**

1. **Is the file being saved to the correct location?**
   - Check upload logs for `savedPath`

2. **Is the file serving route receiving the correct path?**
   - Check file serving logs for `filePath` and `fullPath`

3. **Does the file actually exist where we expect it?**
   - Check file serving logs for `exists: true/false`

4. **Are environment variables consistent?**
   - Compare `env_UPLOAD_DIR` between upload and serving

## 🚀 **Next Steps**

1. **Deploy with debugging** - Push changes to see production logs
2. **Upload a test file** - Trigger the upload process
3. **Access the file URL** - Trigger the file serving route
4. **Check container logs** - Analyze the debug output
5. **Fix based on findings** - Address the root cause

## 🔧 **Potential Issues & Solutions**

### **Issue 1: Path Mismatch**
- **Problem**: Upload saves to one path, serving looks in another
- **Solution**: Ensure consistent path construction

### **Issue 2: File Not Saved**
- **Problem**: Upload API fails silently
- **Solution**: Check upload logs and error handling

### **Issue 3: Environment Variables**
- **Problem**: Different env vars in upload vs serving
- **Solution**: Standardize environment configuration

### **Issue 4: File Permissions**
- **Problem**: File saved but not readable
- **Solution**: Check container file permissions

### **Issue 5: Volume Mounting**
- **Problem**: Files saved to container, not persistent volume
- **Solution**: Verify Docker volume configuration

## 📞 **Debug Commands for Server**

```bash
# Check container logs
docker logs <container_name> | grep -E "(Upload details|File serving request|Attempting to serve)"

# Check file system
docker exec <container> find /app -name "*.jpg" -type f

# Check upload directory
docker exec <container> ls -la /app/static/uploads/payment-proofs/

# Check environment variables
docker exec <container> env | grep UPLOAD_DIR
```

## ✅ **Success Criteria**

Debug is successful when we can see:
- ✅ File uploaded and saved to correct path
- ✅ File serving route receives correct request
- ✅ File exists at expected location
- ✅ File served successfully with proper headers

This debugging will help identify exactly where the disconnect is happening! 🔍
