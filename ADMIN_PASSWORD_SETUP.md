# Admin Password Setup Guide

## 🔐 **FIXED: Admin Password Now Works Properly**

The admin authentication now properly reads from the `.env` file and no longer has hardcoded fallbacks.

## 🚀 **Current Status:**

- ✅ **Default Password**: `admin123` (hash correctly set in .env)
- ✅ **Secure Authentication**: Server-side hashing with session tokens
- ✅ **Environment Variable**: <PERSON><PERSON><PERSON> reads `ADMIN_PASSWORD_HASH` from .env
- ❌ **No Hardcoded Fallbacks**: Removed all hardcoded passwords

## 🔧 **How to Change Admin Password:**

### **Method 1: Interactive Generator (Recommended)**
```bash
# Run the interactive password generator
npm run generate-password

# Follow the prompts:
# 1. Enter your desired password
# 2. Copy the generated hash
# 3. Update .env file
# 4. Restart containers
```

### **Method 2: Manual Generation**
```bash
# Generate hash for your password (replace YOUR_PASSWORD)
node -e "console.log(require('crypto').createHash('sha256').update('YOUR_PASSWORD' + 'ipman_salt_2024').digest('hex'))"

# Example for password "mySecurePassword123":
node -e "console.log(require('crypto').createHash('sha256').update('mySecurePassword123' + 'ipman_salt_2024').digest('hex'))"
```

### **Method 3: Using the Test Script**
```bash
# This will show you the hash for any password
npm run test-admin
```

## 📝 **Step-by-Step Password Change:**

### **Step 1: Generate New Hash**
```bash
# Interactive method (easiest)
npm run generate-password

# Or manual method
node -e "console.log(require('crypto').createHash('sha256').update('YOUR_NEW_PASSWORD' + 'ipman_salt_2024').digest('hex'))"
```

### **Step 2: Update .env File**
Edit your `.env` file and replace the `ADMIN_PASSWORD_HASH` line:
```env
# OLD:
ADMIN_PASSWORD_HASH=0049059fd916200f221ea493eaf1ce111f13dc7ec7f3e2a1008a3629310aa1be

# NEW (example):
ADMIN_PASSWORD_HASH=your_generated_hash_here
```

### **Step 3: Restart Application**
```bash
# Restart to pick up new environment variables
docker-compose restart

# Or full rebuild if needed
docker-compose down
docker-compose up -d
```

### **Step 4: Test Login**
1. Go to `https://ipman.uk/admin`
2. Enter your new password
3. Should login successfully

## 🔍 **Verification Commands:**

### **Test Current Password Hash:**
```bash
# Verify the hash in .env matches your password
npm run test-admin
```

### **Test System:**
```bash
# Test entire system including auth
npm run test-system
```

### **Check Environment Variables:**
```bash
# View current hash in .env
grep ADMIN_PASSWORD_HASH .env
```

## 🛠️ **Troubleshooting:**

### **Issue: "Invalid Password" Error**
1. **Check hash generation**:
   ```bash
   npm run test-admin
   ```
2. **Verify .env file**:
   ```bash
   grep ADMIN_PASSWORD_HASH .env
   ```
3. **Restart containers**:
   ```bash
   docker-compose restart
   ```

### **Issue: API Not Working**
1. **Check Docker logs**:
   ```bash
   docker-compose logs app
   ```
2. **Test API directly**:
   ```bash
   curl -X POST https://ipman.uk/api/admin/auth \
     -H "Content-Type: application/json" \
     -d '{"password":"your_password"}'
   ```

### **Issue: Environment Variable Not Loading**
1. **Check .env file location** (should be in project root)
2. **Verify no extra spaces** around the hash
3. **Restart containers** to pick up changes

## 🔒 **Security Best Practices:**

### **Strong Password Requirements:**
- Minimum 12 characters
- Mix of uppercase, lowercase, numbers, symbols
- Not a common dictionary word
- Unique to this application

### **Examples of Strong Passwords:**
- `MyVPN@dm1n2024!`
- `SecureIPman#789`
- `AdminVPN$tr0ng99`

### **Security Notes:**
- The password hash is safe to store in .env
- Never share the actual password
- Change password regularly
- Monitor admin access logs

## 📋 **Quick Reference:**

### **Current Default:**
- **Password**: `admin123`
- **Hash**: `0049059fd916200f221ea493eaf1ce111f13dc7ec7f3e2a1008a3629310aa1be`

### **Common Commands:**
```bash
# Generate new password hash
npm run generate-password

# Test current setup
npm run test-admin

# Restart after changes
docker-compose restart

# Check logs
docker-compose logs app
```

### **File Locations:**
- **Environment Variables**: `.env` (project root)
- **Auth API**: `src/routes/api/admin/auth/+server.ts`
- **Admin Login**: `src/routes/admin/+layout.svelte`

## ✅ **Verification Checklist:**

- [ ] Generated new password hash
- [ ] Updated `ADMIN_PASSWORD_HASH` in .env file
- [ ] Restarted Docker containers
- [ ] Tested login with new password
- [ ] Confirmed admin dashboard access
- [ ] Verified no hardcoded passwords remain

## 🎉 **Success!**

Your admin authentication is now:
- ✅ **Secure**: Server-side hashing with session tokens
- ✅ **Configurable**: Uses environment variables
- ✅ **No Hardcoded Passwords**: All authentication goes through .env
- ✅ **Production Ready**: Proper security implementation

**The admin password will now change when you update the .env file!**
