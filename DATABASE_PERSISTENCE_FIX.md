# 🗄️ Database Persistence Fix Guide

## 🚨 **Critical Issue Identified**

Your database was being deleted on every deployment because of incorrect Docker volume mapping.

### **Previous Problem:**
```yaml
# WRONG - This overwrites the entire prisma directory
volumes:
  - ./prisma:/app/prisma
```

### **Root Cause:**
1. **Docker volume mapping** was mounting the entire `./prisma` directory
2. **On rebuild**, the container's `/app/prisma` directory (including `dev.db`) was overwritten
3. **Database lost** all invoice information and payment verifications
4. **New empty database** created on each deployment

## ✅ **Solution Implemented**

### **1. Fixed Docker Volume Mapping**
```yaml
# CORRECT - Only mount the database file
volumes:
  - ./data/dev.db:/app/prisma/dev.db
```

### **2. Created Persistent Data Directory**
- **Host directory**: `./data/` (on your server)
- **Database file**: `./data/dev.db` (persistent across deployments)
- **Container path**: `/app/prisma/dev.db` (where app expects it)

### **3. Updated Setup Script**
- **Automatically copies** existing database to persistent location
- **Creates data directory** structure
- **Preserves database** across container rebuilds

## 🔧 **Files Modified**

### **1. `docker-compose.yml`**
```yaml
volumes:
  - ./uploads:/app/static/uploads
  - ./data/dev.db:/app/prisma/dev.db  # ← Fixed: Only mount DB file
```

### **2. `scripts/setup-directories.sh`**
- Added data directory creation
- Added database file copying logic
- Ensures persistence across deployments

### **3. `.env`**
- Added missing `UPLOAD_DIR` environment variable
- Fixed upload path configuration

## 🚀 **Deployment Process**

### **Before First Deployment:**
1. **Run setup script** (already done locally):
   ```bash
   ./scripts/setup-directories.sh
   ```

2. **Verify data directory** exists with database:
   ```bash
   ls -la data/
   # Should show: dev.db
   ```

### **On Your Server (CapRover):**
When you deploy, the GitHub Actions will:
1. **Build new container** with latest code
2. **Mount persistent database** from `./data/dev.db`
3. **Preserve all existing data** (invoices, payments, etc.)
4. **Upload directory** remains intact

## 📁 **Directory Structure**

### **On Your Server:**
```
/your-app-directory/
├── data/
│   └── dev.db                    ← Persistent database (NEVER deleted)
├── uploads/
│   └── payment-proofs/           ← Persistent uploads (NEVER deleted)
├── docker-compose.yml
└── .env
```

### **Inside Container:**
```
/app/
├── prisma/
│   └── dev.db                    ← Mounted from host ./data/dev.db
├── static/uploads/
│   └── payment-proofs/           ← Mounted from host ./uploads/
└── build/                        ← App code (rebuilt on each deploy)
```

## 🛡️ **Data Safety Guarantees**

### **✅ What's Now Protected:**
- **Database file** (`dev.db`) - Persistent across deployments
- **Uploaded files** - Persistent across deployments
- **Invoice data** - Never lost on deployment
- **Payment verifications** - Never lost on deployment

### **🔄 What Gets Updated:**
- **Application code** - Updated on each deployment
- **Dependencies** - Updated when package.json changes
- **Environment variables** - Updated from .env file

## 🧪 **Testing the Fix**

### **Before Deployment:**
1. **Check current data**:
   ```bash
   ls -la data/dev.db
   ls -la uploads/payment-proofs/
   ```

2. **Verify database content** (optional):
   ```bash
   sqlite3 data/dev.db ".tables"
   ```

### **After Deployment:**
1. **Verify data persistence**:
   - Login to admin panel
   - Check if existing invoices are still there
   - Verify payment verifications are intact

2. **Test new uploads**:
   - Upload a new payment proof
   - Verify it's accessible via URL
   - Check admin panel shows the new upload

## 🚨 **Important Notes**

### **Server Setup Required:**
On your CapRover server, you need to create the data directory:
```bash
# SSH into your server
mkdir -p /path/to/your/app/data
mkdir -p /path/to/your/app/uploads/payment-proofs

# Copy existing database if available
# (This step may not be needed if database is already lost)
```

### **Backup Recommendation:**
```bash
# Regular database backups
cp data/dev.db data/dev.db.backup.$(date +%Y%m%d_%H%M%S)
```

## 🎯 **Expected Results**

After this fix:
- ✅ **Database survives deployments**
- ✅ **Invoice data preserved**
- ✅ **Payment verifications preserved**
- ✅ **Uploaded files accessible**
- ✅ **New uploads work correctly**
- ✅ **Admin panel functions properly**

## 🔄 **Migration Steps**

If you've already lost data, this fix will:
1. **Prevent future data loss**
2. **Start with a clean, persistent database**
3. **Preserve all new data going forward**

The lost data cannot be recovered, but this ensures it never happens again.

## 📞 **Verification Commands**

After deployment, verify everything works:
```bash
# Check database file exists and has content
ls -la data/dev.db

# Check uploads directory
ls -la uploads/payment-proofs/

# Test file access (replace with actual filename)
curl -I https://ipman.uk/uploads/payment-proofs/some-file.jpg
```

This fix ensures your database and uploaded files will persist across all future deployments! 🎉
