# 📁 Upload Files Fix Guide

## 🔍 **Problem Identified**

Uploaded payment proof images were returning 404 errors when accessed via URLs like `/uploads/payment-proofs/photo_2025-05-06_13-21-59.jpg`.

### **Root Cause:**
1. **Local Development**: Files uploaded to `static/uploads/payment-proofs/` and served correctly by SvelteKit
2. **Production (Docker)**: Files uploaded to `/app/static/uploads/payment-proofs/` inside container, but static file serving not configured properly
3. **Volume Mapping Issue**: Docker volume mapping expected `./uploads` directory on host, but it didn't exist
4. **Static File Serving**: SvelteKit's adapter-node doesn't automatically serve uploaded files in production

## ✅ **Solutions Implemented**

### **1. Created Dynamic File Serving Route**
- **File**: `src/routes/uploads/[...path]/+server.ts`
- **Purpose**: Serves uploaded files dynamically through SvelteKit API routes
- **Features**:
  - Security: Prevents directory traversal attacks
  - Content-Type detection based on file extension
  - Proper caching headers
  - Error handling for missing files

### **2. Fixed Upload API Configuration**
- **File**: `src/routes/api/upload-payment-proof/+server.ts`
- **Changes**:
  - Uses environment variable `UPLOAD_DIR` instead of hardcoded path
  - Supports both development and production paths
  - Proper error handling for directory creation

### **3. Enhanced Docker Configuration**
- **File**: `Dockerfile`
- **Changes**:
  - Creates upload directories during build
  - Runs setup script on container startup
  - Ensures proper permissions for upload directories

### **4. Added Production Setup Script**
- **File**: `scripts/setup-production-directories.sh`
- **Purpose**: Ensures upload directories exist in production environment
- **Features**:
  - Creates necessary directories
  - Sets proper permissions
  - Handles ownership for non-root user

### **5. File Migration Script**
- **File**: `scripts/migrate-uploaded-files.js`
- **Purpose**: Migrates existing files from `static/uploads` to proper upload directory
- **Usage**: `npm run migrate-files`

## 🚀 **Deployment Instructions**

### **For Current Deployment:**
1. **Commit and push** all changes to trigger GitHub Actions
2. **Wait for deployment** to complete on CapRover
3. **Test file access** by visiting uploaded image URLs

### **For Manual Server Setup:**
```bash
# 1. Create upload directories on server
mkdir -p ./uploads/payment-proofs
chmod 755 ./uploads ./uploads/payment-proofs

# 2. Migrate existing files (if any)
npm run migrate-files

# 3. Restart containers
docker-compose restart
```

## 🔧 **Environment Variables**

### **Development (.env.local):**
```env
UPLOAD_DIR=./static/uploads/payment-proofs
```

### **Production (.env):**
```env
UPLOAD_DIR=/app/static/uploads/payment-proofs
```

## 📂 **Directory Structure**

### **Development:**
```
project/
├── static/uploads/payment-proofs/  (Local development uploads)
└── src/routes/uploads/[...path]/   (File serving route)
```

### **Production (Docker):**
```
/app/
├── static/uploads/payment-proofs/  (Container uploads)
├── scripts/setup-production-directories.sh
└── src/routes/uploads/[...path]/   (File serving route)
```

## 🧪 **Testing**

### **Test File Upload:**
1. Go to your website's upload form
2. Upload a payment proof image
3. Note the returned file URL

### **Test File Access:**
1. Copy the file URL from upload response
2. Visit the URL directly in browser
3. Image should display correctly

### **Test Admin Panel:**
1. Login to admin panel
2. View payment verifications
3. Click "View Image" buttons
4. Images should display in modal

## 🔍 **Troubleshooting**

### **404 Errors Still Occurring:**
1. Check container logs: `docker logs <container_name>`
2. Verify upload directory exists: `docker exec <container> ls -la /app/static/uploads/`
3. Check file permissions: `docker exec <container> ls -la /app/static/uploads/payment-proofs/`

### **Upload Failures:**
1. Check disk space on server
2. Verify upload directory permissions
3. Check container logs for errors

### **File Migration Issues:**
```bash
# Run migration script manually
npm run migrate-files

# Check source directory
ls -la static/uploads/payment-proofs/

# Check destination directory
ls -la uploads/payment-proofs/
```

## 📝 **File URL Format**

### **Correct URL Format:**
```
https://ipman.uk/uploads/payment-proofs/filename.jpg
```

### **How It Works:**
1. Request hits SvelteKit route: `/uploads/[...path]`
2. Route extracts file path: `payment-proofs/filename.jpg`
3. Route reads file from: `/app/static/uploads/payment-proofs/filename.jpg`
4. Route serves file with proper headers

## ✅ **Verification Checklist**

- [ ] Files upload successfully
- [ ] File URLs return images (not 404)
- [ ] Admin panel displays images correctly
- [ ] Image modal works in admin panel
- [ ] No console errors related to file serving
- [ ] Proper caching headers are set
- [ ] Security: Directory traversal protection works

## 🎉 **Expected Results**

After deployment:
- ✅ All uploaded images accessible via `/uploads/payment-proofs/filename.jpg`
- ✅ Admin panel image viewing works correctly
- ✅ New uploads work properly
- ✅ Existing files migrated and accessible
- ✅ Proper error handling for missing files
- ✅ Security measures in place
