# WooCommerce API credentials
VITE_WOOCOMMERCE_URL=https://your-woocommerce-site.com
VITE_WOOCOMMERCE_CONSUMER_KEY=your_consumer_key
VITE_WOOCOMMERCE_CONSUMER_SECRET=your_consumer_secret

# Together AI API credentials
TOGETHER_AI_API_KEY=your_together_ai_api_key

# OpenRouter AI API credentials
OPENROUTER_API_KEY=your_openrouter_api_key

# Meta Pixel (Facebook Pixel) credentials
PUBLIC_META_PIXEL_ID=your_meta_pixel_id
META_CONVERSION_API_TOKEN=your_meta_conversion_api_token

# --- Docker Deployment Variables ---

# Upload directory path *inside* the app container
# This is a private variable used by server-side code (e.g., cleanup script)
UPLOAD_DIR=/app/static/uploads/payment-proofs

# Domain name for Caddy (used in Caddyfile)
# Replace localhost with your actual domain if deploying publicly
YOUR_DOMAIN=localhost

# Caddy Basic Authentication credentials for protected routes (e.g., /ocr-test)
# These are private variables used by the Caddy service
CADDY_ADMIN_USER=admin
# Generate hash using: docker compose run --rm caddy caddy hash-password
CADDY_ADMIN_PASSWORD_HASH=your_generated_caddy_password_hash

# --- Application Port ---
# Port the SvelteKit app listens on inside the container
PORT=3000

# --- Database ---
# Example for SQLite (adjust path if needed)
# DATABASE_URL="file:/app/prisma/dev.db"
# Example for PostgreSQL (uncomment and adjust if using the db service in docker-compose)
# DATABASE_URL="postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}?schema=public"
# POSTGRES_DB=mydb
# POSTGRES_USER=myuser
# POSTGRES_PASSWORD=mypassword
