// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  // Generate client to a different location as recommended by Prisma
  output        = "../node_modules/.prisma/client"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x"] // For Alpine Linux runtime
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL") // Use an environment variable
}

model PaymentVerification {
  id                Int      @id @default(autoincrement())
  transaction_id    String   @unique
  order_id          Int?
  payment_service   String?
  verification_date DateTime @default(now())
  image_path        String?
  verification_status String  @default("verified")
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt

  @@index([transaction_id])
  @@index([order_id])
}
