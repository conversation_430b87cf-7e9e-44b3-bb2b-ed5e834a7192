import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

const jsonData = [
  {
    "transaction_id": "01003757070077944907",
    "order_id": 4677,
    "payment_service": "KBZ Bank",
    "verification_status": "verified",
    "id": 1,
    "verification_date": "2025-04-15T10:46:25.984Z",
    "created_at": "2025-04-15T10:46:25.984Z"
  },
  {
    "transaction_id": "*********",
    "order_id": 4677,
    "payment_service": "KBZ Pay",
    "verification_status": "verified",
    "id": 2,
    "verification_date": "2025-04-15T11:03:06.494Z",
    "created_at": "2025-04-15T11:03:06.494Z"
  },
  {
    "transaction_id": "*********",
    "order_id": 4677,
    "payment_service": "Unknown",
    "verification_status": "verified",
    "id": 3,
    "verification_date": "2025-04-15T12:36:05.040Z",
    "created_at": "2025-04-15T12:36:05.040Z"
  },
  {
    "transaction_id": "01003759070129747697",
    "order_id": 141,
    "payment_service": "Unknown",
    "verification_status": "verified",
    "id": 4,
    "verification_date": "2025-04-17T17:17:10.978Z",
    "created_at": "2025-04-17T17:17:10.978Z"
  }
];

// Prepare data for seeding: remove 'id' and convert date strings to Date objects
const dataToSeed = jsonData.map(item => ({
  transaction_id: item.transaction_id,
  order_id: item.order_id,
  payment_service: item.payment_service,
  verification_status: item.verification_status,
  // Convert date strings to Date objects
  verification_date: new Date(item.verification_date),
  created_at: new Date(item.created_at),
  // image_path is omitted as it's not in jsonData and nullable in schema
  // updated_at is handled by @updatedAt
}));


async function main(): Promise<void> {
  console.log('Preparing data for seeding...');
  console.log(`Attempting to seed ${dataToSeed.length} records.`);

  try {
      console.log('Starting data insertion...');
      const result = await prisma.paymentVerification.createMany({
          data: dataToSeed, // Use the processed data
          // skipDuplicates: true, // Not supported for SQLite in this Prisma version
      });
      console.log(`${result.count} records inserted into the database.`);
      console.log('Data seeded successfully.');
  } catch (error) {
      console.error('Error seeding data:', error);
      // Consider handling specific error types (e.g., unique constraint violation)
  } finally {
      await prisma.$disconnect();
  }
}


main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('Error in seeding process:', e); // More specific log
    await prisma.$disconnect();
    // Explicitly reject the promise to satisfy type checking and propagate error
    return Promise.reject(new Error("Seeding failed. See logs for details."));
  })
