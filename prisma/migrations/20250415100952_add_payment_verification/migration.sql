-- CreateTable
CREATE TABLE "PaymentVerification" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "transaction_id" TEXT NOT NULL,
    "order_id" INTEGER NOT NULL,
    "payment_service" TEXT,
    "verification_date" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "image_path" TEXT,
    "verification_status" TEXT NOT NULL DEFAULT 'verified',
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "PaymentVerification_transaction_id_key" ON "PaymentVerification"("transaction_id");

-- CreateIndex
CREATE INDEX "PaymentVerification_transaction_id_idx" ON "PaymentVerification"("transaction_id");

-- CreateIndex
CREATE INDEX "PaymentVerification_order_id_idx" ON "PaymentVerification"("order_id");
