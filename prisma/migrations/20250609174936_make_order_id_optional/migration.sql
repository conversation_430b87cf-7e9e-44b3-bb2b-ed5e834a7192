-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_PaymentVerification" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "transaction_id" TEXT NOT NULL,
    "order_id" INTEGER,
    "payment_service" TEXT,
    "verification_date" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "image_path" TEXT,
    "verification_status" TEXT NOT NULL DEFAULT 'verified',
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);
INSERT INTO "new_PaymentVerification" ("created_at", "id", "image_path", "order_id", "payment_service", "transaction_id", "updated_at", "verification_date", "verification_status") SELECT "created_at", "id", "image_path", "order_id", "payment_service", "transaction_id", "updated_at", "verification_date", "verification_status" FROM "PaymentVerification";
DROP TABLE "PaymentVerification";
ALTER TABLE "new_PaymentVerification" RENAME TO "PaymentVerification";
CREATE UNIQUE INDEX "PaymentVerification_transaction_id_key" ON "PaymentVerification"("transaction_id");
CREATE INDEX "PaymentVerification_transaction_id_idx" ON "PaymentVerification"("transaction_id");
CREATE INDEX "PaymentVerification_order_id_idx" ON "PaymentVerification"("order_id");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
