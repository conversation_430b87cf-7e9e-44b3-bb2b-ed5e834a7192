# WooCommerce Headless CMS Integration

This document provides instructions on how to set up and use the WooCommerce integration for the IPman VPN website.

## Prerequisites

1. A WordPress site with WooCommerce installed and configured
2. WooCommerce REST API credentials (Consumer Key and Consumer Secret)

## Setup Instructions

### 1. Create WooCommerce API Keys

1. Log in to your WordPress admin dashboard
2. Go to WooCommerce > Settings > Advanced > REST API
3. Click "Add Key"
4. Enter a description (e.g., "IPman VPN Website")
5. Set User to an admin account
6. Set Permissions to "Read/Write"
7. Click "Generate API Key"
8. Copy the Consumer Key and Consumer Secret

### 2. Configure Environment Variables

1. Create a `.env` file in the root of your project (copy from `.env.example`)
2. Add your WooCommerce API credentials:

```
VITE_WOOCOMMERCE_URL=https://your-wordpress-site.com
VITE_WOOCOMMERCE_CONSUMER_KEY=your_consumer_key
VITE_WOOCOMMERCE_CONSUMER_SECRET=your_consumer_secret
```

### 3. Start the Development Server

```bash
npm run dev
```

## Features

The WooCommerce integration provides the following features:

1. **Product Listing**: Display all products from your WooCommerce store
2. **Product Categories**: Filter products by category
3. **Product Details**: View detailed information about each product

## Implementation Details

### API Service

The WooCommerce API service is implemented in `src/lib/services/woocommerce.ts`. It includes:

- A custom WooCommerce API client that uses the native fetch API instead of axios
- Functions for fetching all products, a single product by ID, product categories, and products by category
- TypeScript interfaces for WooCommerce data types

### Server Routes

Server-side API routes are implemented in:

- `src/routes/api/woocommerce/products/+server.ts`: Get all products
- `src/routes/api/woocommerce/products/[id]/+server.ts`: Get a single product
- `src/routes/api/woocommerce/categories/+server.ts`: Get all categories

### Pages

- `src/routes/products/+page.svelte`: Product listing page
- `src/routes/products/[id]/+page.svelte`: Product detail page

## Customization

You can customize the appearance of the product listing and detail pages by modifying the respective Svelte components.

## Troubleshooting

If you encounter issues with the WooCommerce integration:

1. Check that your API credentials are correct
2. Ensure your WordPress site is accessible
3. Check that CORS is properly configured on your WordPress site
4. Check the browser console for any error messages

## Additional Resources

- [WooCommerce REST API Documentation](https://woocommerce.github.io/woocommerce-rest-api-docs/)
- [SvelteKit Documentation](https://kit.svelte.dev/docs)
