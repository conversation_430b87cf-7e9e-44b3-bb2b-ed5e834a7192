# 🚀 Deployment Checklist - Upload & Database Fixes

## 📋 **Pre-Deployment Checklist**

### ✅ **Files Modified/Created:**
- [ ] `docker-compose.yml` - Fixed database volume mapping
- [ ] `.env` - Added UPLOAD_DIR environment variable
- [ ] `src/routes/uploads/[...path]/+server.ts` - File serving route
- [ ] `src/routes/api/upload-payment-proof/+server.ts` - Fixed upload API
- [ ] `scripts/setup-directories.sh` - Enhanced setup script
- [ ] `scripts/setup-production-directories.sh` - Production setup
- [ ] `Dockerfile` - Enhanced with directory creation
- [ ] `DATABASE_PERSISTENCE_FIX.md` - Documentation
- [ ] `UPLOAD_FIX_GUIDE.md` - Documentation

### ✅ **Local Testing Completed:**
- [ ] Database copied to `data/dev.db`
- [ ] Upload directories created
- [ ] 35 files migrated successfully
- [ ] File serving route implemented

## 🎯 **Expected Results After Deployment**

### **Database Persistence:**
- ✅ Database survives container rebuilds
- ✅ Invoice data preserved
- ✅ Payment verifications preserved
- ✅ Admin login works with existing password

### **File Upload & Serving:**
- ✅ New uploads work correctly
- ✅ Uploaded files accessible via URLs
- ✅ Admin panel image viewing works
- ✅ No more 404 errors for images

## 🔧 **Server-Side Setup Required**

### **On Your CapRover Server:**
After deployment, ensure these directories exist:
```bash
# SSH into your server and navigate to app directory
mkdir -p ./data
mkdir -p ./uploads/payment-proofs
chmod 755 ./data ./uploads ./uploads/payment-proofs
```

### **If Database Already Lost:**
If your database was already deleted, the new deployment will:
1. Create a fresh database
2. Ensure it persists for all future deployments
3. Start collecting new data that will never be lost again

## 🧪 **Post-Deployment Testing**

### **1. Test Database Persistence:**
```bash
# Check if database file exists
ls -la data/dev.db

# Verify admin login works
# Visit: https://ipman.uk/admin
```

### **2. Test File Upload:**
```bash
# Upload a payment proof through the website
# Note the returned file URL
# Visit the URL directly to verify it works
```

### **3. Test Admin Panel:**
```bash
# Login to admin panel
# Go to Payment Verifications
# Click "View Image" on any verification
# Verify images display correctly
```

## 🚨 **Troubleshooting Guide**

### **If Database Issues Persist:**
```bash
# Check database file exists
docker exec <container> ls -la /app/prisma/dev.db

# Check volume mounting
docker inspect <container> | grep Mounts
```

### **If Upload Issues Persist:**
```bash
# Check upload directory
docker exec <container> ls -la /app/static/uploads/payment-proofs/

# Check environment variables
docker exec <container> env | grep UPLOAD_DIR

# Test file serving route
curl -I https://ipman.uk/uploads/payment-proofs/test-file.jpg
```

### **If 404 Errors Continue:**
1. Check container logs: `docker logs <container>`
2. Verify file serving route is deployed
3. Check file permissions in upload directory
4. Verify environment variables are loaded

## 📞 **Support Commands**

### **Check Container Status:**
```bash
docker ps
docker logs <container_name>
```

### **Check File Structure:**
```bash
docker exec <container> find /app -name "*.db" -o -name "payment-proofs"
```

### **Check Environment:**
```bash
docker exec <container> env | grep -E "(DATABASE_URL|UPLOAD_DIR)"
```

## ✅ **Success Indicators**

After successful deployment, you should see:
- ✅ Admin login works
- ✅ Database data persists between deployments
- ✅ New payment proof uploads work
- ✅ Uploaded images accessible via direct URLs
- ✅ Admin panel shows images correctly
- ✅ No 404 errors for uploaded files

## 🎉 **Final Notes**

This deployment includes critical fixes that will:
1. **Prevent future data loss** - Database now persists
2. **Fix upload functionality** - Files properly served
3. **Improve reliability** - Proper error handling
4. **Enhance security** - Directory traversal protection

Your application will be much more stable and reliable after this deployment! 🚀

## 📝 **Commit Message Suggestion**

```
🔧 Fix critical database persistence and file upload issues

- Fix database deletion on deployment (volume mapping)
- Add file serving route for uploaded images
- Fix upload API environment configuration
- Add production directory setup scripts
- Migrate existing files to proper structure
- Add comprehensive documentation

Fixes #database-loss #upload-404-errors
```
